import type { Client } from 'discord.js'
import { RotateMonitor } from 'services/monitor/rotation/service'

import type {
  AllianceMember,
  AllianceMembersResponse
} from '../../interfaces/alliance'
import type { InstanceConfig } from '../../interfaces/config'
import { BotError } from '../../utils/common/error'
import { validateConfig } from '../../utils/config/config-validator'
import { logger } from '../../utils/logging/logger'
import { getApiInstance } from '../api/api-instance'

let rotateMonitor: RotateMonitor | null = null

export async function fetchTotalMembers(
  instanceConfig: InstanceConfig
): Promise<number> {
  try {
    const api = getApiInstance(instanceConfig.continent)
    const response = await api.post<{ alliance: { numMembers: number } }>(
      '/alliance/info/my',
      {}
    )

    if (response?.alliance?.numMembers !== undefined) {
      return response.alliance.numMembers
    } else {
      return 0
    }
  } catch (error) {
    logger.error('Error fetching total members:', error)
    return 0
  }
}

export async function fetchAllianceMembersRotate(
  instanceConfig: InstanceConfig
): Promise<AllianceMember[]> {
  try {
    const api = getApiInstance(instanceConfig.continent)
    const response = await api.post<AllianceMembersResponse>(
      '/alliance/members/list',
      { allianceId: '' }
    )

    if (!response || !response.members) {
      return []
    }

    const group = response.members.find(g => g._id === 1)
    return group ? group.members : []
  } catch (error) {
    throw new BotError(
      'Failed to fetch alliance members',
      'ALLIANCE_MEMBER_ERROR'
    )
  }
}

export async function openAlliance(
  instanceConfig: InstanceConfig
): Promise<{ result: boolean; message: string }> {
  return changeAllianceStatus('1', instanceConfig)
}

export async function closeAlliance(
  instanceConfig: InstanceConfig
): Promise<{ result: boolean; message: string }> {
  return changeAllianceStatus('2', instanceConfig)
}

async function changeAllianceStatus(
  allowType: string,
  instanceConfig: InstanceConfig
): Promise<{ result: boolean; message: string }> {
  try {
    const api = getApiInstance(instanceConfig.continent)
    const response = await api.post<{ result: boolean }>(
      '/alliance/change/allowType',
      { allowType }
    )

    if (response.result) {
      if (instanceConfig.rotate) {
        instanceConfig.rotate.isOpen = allowType === '1'
      }
      return {
        result: true,
        message:
          allowType === '1'
            ? 'Alliance opened successfully!'
            : 'Alliance closed successfully!'
      }
    } else {
      return {
        result: false,
        message:
          'Failed to change alliance status. Invalid response from the server.'
      }
    }
  } catch (error) {
    if (error instanceof BotError && error.code === 'no_alliance_auth') {
      return {
        result: false,
        message:
          'Account does not have the King role. Please transfer the King role to this account and try again.'
      }
    }
    return {
      result: false,
      message: 'An error occurred while trying to change the alliance status.'
    }
  }
}

export async function inviteMember(
  instanceConfig: InstanceConfig,
  kingdomId: string
): Promise<boolean> {
  try {
    const api = getApiInstance(instanceConfig.continent)
    const response = await api.post<{
      result: boolean
      err?: { code: string }
    }>('/alliance/invite', { kingdomId })

    if (response.err?.code === 'already_invited') {
      logger.info(`Skipping member ${kingdomId} - already invited`)
      return false
    }

    return response.result
  } catch (error) {
    logger.error(`Failed to invite member ${kingdomId}:`, error)
    return false
  }
}

export async function kickMember(
  instanceConfig: InstanceConfig,
  kingdomId: string
): Promise<boolean> {
  try {
    const api = getApiInstance(instanceConfig.continent)
    const response = await api.post<{
      result: boolean
      err?: { code: string }
    }>('/alliance/member/disband', {
      memberKingdomId: kingdomId
    })

    if (response.err?.code === 'during_rally') {
      logger.info(`Skipping member ${kingdomId} - currently in rally`)
      return false
    }

    return response.result
  } catch (error) {
    logger.error(`Error kicking member ${kingdomId}:`, error)
    return false
  }
}

export function setupRotateMonitor(
  client: Client,
  instanceConfig: InstanceConfig
): void {
  if (!validateConfig(instanceConfig)) {
    throw new BotError('Invalid instance configuration', 'INVALID_CONFIG_ERROR')
  }

  const rotateConfig = instanceConfig.rotate

  if (!rotateConfig || !rotateConfig.enabled) {
    throw new BotError(
      `Rotate monitoring not configured or not enabled for Continent ${instanceConfig.continent}`,
      'ROTATE_CONFIG_ERROR'
    )
  }

  if (rotateMonitor) {
    rotateMonitor.stop()
  }

  rotateMonitor = new RotateMonitor(client, instanceConfig)
  rotateMonitor.start()
}

export function stopRotateMonitor(): void {
  if (rotateMonitor) {
    rotateMonitor.stop()
    rotateMonitor = null
  }
}
