import type {
  ChatInputCommandInteraction,
  SlashCommandBuilder
} from 'discord.js'
import { ApiService } from 'services/api/api-service.js'
import { SocketService } from 'services/socket/service.js'
import {
 RoleStatusMap, TitleToggleMap
} from 'services/title/types.js'

import type { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import type { InstanceConfig } from '../../interfaces/config.js'

export interface Command {
  data: SlashCommandBuilder | any
  CommandHandlerClass: new (
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap?: TitleToggleMap,
    roleStatusMap?: RoleStatusMap,
    socketService?: SocketService
  ) => BaseCommandHandler
}

export interface CommandModule {
  data: SlashCommandBuilder
  default: new (
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig
  ) => BaseCommandHandler
}
