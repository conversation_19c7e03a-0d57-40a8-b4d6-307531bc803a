import { BotError } from 'utils/common/error.js'

import { getApiInstance } from '../api/api-instance'

import type { InstanceConfig } from '../../interfaces/config'
import type {
 SkillData, SkillUseResult 
} from './types.js'

const skillNameMap = {
  '101': 'Global Gathering',
  '102': 'Global Healing',
  '103': 'Global Training Speed Up',
  '104': 'Spawn Crystal Mine'
}

export function getSkillName(skillCode: string): string {
  return skillNameMap[skillCode as keyof typeof skillNameMap] || 'Unknown Skill'
}

export async function fetchSkillData(
  instanceConfig: InstanceConfig
): Promise<SkillData[]> {
  const api = getApiInstance(instanceConfig.continent)
  const response = await api.post<{ result: boolean; skills: SkillData[] }>(
    '/shrine/skill',
    {}
  )

  if (!response.result || !Array.isArray(response.skills)) {
    throw new BotError(
      'Invalid response structure from API',
      'SKILL_DATA_ERROR'
    )
  }

  return response.skills
}

export async function activateGlobalSkill(
  instanceConfig: InstanceConfig,
  skillCode: string
): Promise<SkillUseResult> {
  const api = getApiInstance(instanceConfig.continent)
  const response = await api.post<{
    result: boolean
    duration: number
    endTime: number
  }>('/shrine/skill/use', {
    code: skillCode
  })

  if (!response.result) {
    throw new BotError('Failed to use global skill', 'USE_SKILL_ERROR')
  }

  return {
    result: true,
    duration: response.duration,
    endTime: response.endTime,
    skillName: getSkillName(skillCode)
  }
}
