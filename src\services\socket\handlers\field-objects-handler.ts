import type { KingdomObject } from "../../../interfaces/object-types.js"
import { logger } from "../../../utils/logging/logger.js"
import { socketEvents } from "../utils/event-dispatcher.js"

/**
 * Handles field object update from the socket
 * This processes a single object update from the /field/object/update event
 *
 * The socket service has already parsed the message from:
 * 42["/field/object/update",{"fo":{...}}]
 * to just the payload part: {"fo":{...}}
 */
export function handleFieldObject(data: unknown): void {
  // Log the entire raw data we receive with a distinctive marker for easier log searching
  logger.info(`[FIELD_OBJECT_UPDATE] Received data: ${JSON.stringify(data)}`)

  if (!data) {
    logger.warn("[FIELD_OBJECT_UPDATE] Received empty field object data")
    return
  }

  // The data should be in the format: { fo: { ... } }
  const fieldObjectData = data as { fo?: KingdomObject }

  // Now process the field object data
  if (fieldObjectData && fieldObjectData.fo) {
    const fieldObject = fieldObjectData.fo

    // Check if this is a kingdom (code 20300101) and it's occupied
    if (fieldObject.code === 20300101 && fieldObject.occupied) {
      logger.info(`[FIELD_OBJECT_UPDATE] Found kingdom object with code 20300101: ${JSON.stringify(fieldObject)}`)

      // Process the location array if needed
      let locContinent, locX, locY

      // Handle the case where loc might be an array [continent, x, y]
      if (Array.isArray(fieldObject.loc)) {
        const locArray = fieldObject.loc as unknown as number[]
        locContinent = locArray[0]
        locX = locArray[1]
        locY = locArray[2]

        // Create a properly formatted KingdomObject with the correct loc structure
        const kingdomObject: KingdomObject = {
          _id: fieldObject._id,
          loc: {
            continent: locContinent,
            x: locX,
            y: locY
          },
          level: fieldObject.level,
          code: fieldObject.code,
          occupied: fieldObject.occupied
        }

        logger.info(
          `[FIELD_OBJECT_UPDATE] New kingdom detected: ${kingdomObject.occupied.name} at [${locContinent},${locX},${locY}]`
        )

        // Dispatch the event with the kingdom object for the eject service to use
        logger.info(`[FIELD_OBJECT_UPDATE] Dispatching event for kingdom: ${kingdomObject._id}`)
        socketEvents.dispatch("field:object/update", { objects: [kingdomObject] })
      } else {
        // The loc is already in the correct format
        logger.info(
          `[FIELD_OBJECT_UPDATE] New kingdom detected: ${fieldObject.occupied.name} at [${fieldObject.loc.continent},${fieldObject.loc.x},${fieldObject.loc.y}]`
        )

        // Dispatch the event with the kingdom object for the eject service to use
        logger.info(`[FIELD_OBJECT_UPDATE] Dispatching event for kingdom: ${fieldObject._id}`)
        socketEvents.dispatch("field:object/update", { objects: [fieldObject] })
      }
    } else if (fieldObject.code) {
      // Not a kingdom or not occupied, but has a code
      logger.debug(`[FIELD_OBJECT_UPDATE] Ignoring object with code ${fieldObject.code} (not a kingdom or not occupied)`)
    }
  } else {
    // If we get here, the data doesn't match our expected format
    logger.warn(`[FIELD_OBJECT_UPDATE] No 'fo' property found in data: ${JSON.stringify(data)}`)
  }
}
