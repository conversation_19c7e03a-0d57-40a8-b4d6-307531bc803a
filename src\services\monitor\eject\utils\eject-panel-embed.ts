import {
  type Client, EmbedBuilder, type TextChannel
} from 'discord.js'

import type { InstanceConfig } from '../../../../interfaces/config.js'
import { logger } from '../../../../utils/logging/logger.js'
import { EjectService } from '../../../eject/eject-service.js'

// Map to store eject service instances by continent
const ejectServices: Record<number, EjectService> = {}

/**
 * Get or create an eject service for a continent
 * @param instanceConfig The instance configuration
 * @returns The eject service
 */
export function getEjectService(instanceConfig: InstanceConfig): EjectService {
  // Use instance continent for service tracking
  const instanceContinent = instanceConfig.continent

  // Check if we need to initialize the service
  if (!ejectServices[instanceContinent]) {
    // Always use instance continent for API calls

    // Create and store the service
    ejectServices[instanceContinent] = new EjectService(instanceConfig)

    logger.debug(`[EjectService] Created service for instance continent ${instanceContinent}`)
  }

  return ejectServices[instanceContinent]
}

/**
 * Create the eject panel embed
 * @param instanceConfig The instance configuration
 * @returns The embed message options
 */
export function createEjectPanel(instanceConfig: InstanceConfig) {
  const ejectService = ejectServices[instanceConfig.continent]
  const ejectedCount = ejectService?.getEjectedCount() || 0
  const isEnabled = instanceConfig.eject?.enabled || false

  // Get field continent for display
  const fieldContinent = instanceConfig.kingdomData?.kingdom?.loc?.[0]

  const embed = new EmbedBuilder()
    .setTitle('CVC Eject Manager Panel')
    .setDescription(
      `Status: ${isEnabled ? '🟢 Enabled' : '🔴 Disabled'}\n\n` +
        `**Current Statistics:**\n` +
        `• Field Continent: ${fieldContinent || 'Unknown'}\n` +
        `• Kingdoms Ejected: ${ejectedCount}` +
        `\n**How it works:**\n` +
        `• Automatically kicks kingdoms in CVC when active\n` +
        `• Use the buttons below to toggle the service`
    )
    .setTimestamp()
    .setColor(isEnabled ? 0x00ff00 : 0xff0000)

  return { embeds: [embed] }
}

/**
 * Update the eject panel
 * @param client The Discord client
 * @param instanceConfig The instance configuration
 */
export async function updateEjectPanel(
  client: Client,
  instanceConfig: InstanceConfig
) {
  if (!instanceConfig.eject?.channelId || !instanceConfig.eject?.messageId) {
    return
  }

  try {
    const channel = (await client.channels.fetch(
      instanceConfig.eject.channelId
    )) as TextChannel
    if (!channel || !channel.isTextBased()) {
      logger.error('Invalid eject panel channel configuration.')
      return
    }

    const message = await channel.messages.fetch(
      instanceConfig.eject.messageId
    )
    await message.edit(createEjectPanel(instanceConfig))
  } catch (error) {
    logger.error('Error updating eject panel:', error)
  }
}

/**
 * Update the eject panel embed with the latest information
 * @param client The Discord client
 * @param instanceConfig The instance configuration
 */
export async function updateEjectPanelEmbed(
  client: Client,
  instanceConfig: InstanceConfig
) {
  // Ensure the eject service exists
  const ejectService = getEjectService(instanceConfig)

  // Set the client for panel updates
  ejectService.setClient(client)

  // Update the panel
  await updateEjectPanel(client, instanceConfig)
}