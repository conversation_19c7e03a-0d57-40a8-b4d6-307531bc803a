import * as esbuild from 'esbuild'
import fs from 'fs/promises'
import path from 'path'

async function setupConfig() {
  const configDir = path.join(process.cwd(), 'config')
  const distDir = path.join(process.cwd(), 'dist')

  try {
    await fs.mkdir(configDir, { recursive: true })
    await fs.mkdir(distDir, { recursive: true })

    const configPath = path.join(configDir, 'config.json')
    try {
      await fs.access(configPath)
    } catch {
      await fs.writeFile(configPath, '[]', 'utf8')
    }

    await fs.copyFile(configPath, path.join(distDir, 'config.json'))
  } catch (error) {
    console.error('Error setting up config:', error)
    throw error
  }
}

async function buildProject() {
  await setupConfig()

  const buildOptions = {
    entryPoints: ['src/index.ts'],
    outfile: 'dist/index.js',
    platform: 'node',
    target: 'node18',
    format: 'esm',
    bundle: true,
    minify: false,
    sourcemap: true,
    loader: {
      '.ts': 'ts',
      '.json': 'json'
    },
    banner: {
      js: "import { createRequire } from 'module';const require = createRequire(import.meta.url);"
    },
    external: [
      'node:*',
      'discord.js',
      '@discordjs/*',
      'zlib-sync',
      'bufferutil',
      'utf-8-validate',
      'erlpack'
    ],
    plugins: [
      {
        name: 'alias-plugin',
        setup(build) {
          build.onResolve({ filter: /^@\// }, args => {
            return { path: path.resolve('src', args.path.slice(2)) }
          })
        }
      }
    ]
  }

  try {
    await esbuild.build(buildOptions)
    console.log('Build complete')
  } catch (error) {
    console.error('Build failed:', error)
    process.exit(1)
  }
}

buildProject().catch(console.error)
