import type { ButtonInteraction } from 'discord.js'

import type { InstanceConfig } from '../../../interfaces/config.js'
import { getEjectService } from '../../../services/monitor/eject/utils/eject-panel-embed.js'
import { setSettings } from '../../../utils/config/config-utils.js'
import { logger } from '../../../utils/logging/logger.js'

export async function handleEjectEnable(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
): Promise<void> {
  try {
    await interaction.deferUpdate()

    const ejectService = getEjectService(instanceConfig)
    ejectService.setClient(interaction.client)
    ejectService.startListening()

    instanceConfig.eject.enabled = true
    await setSettings(instanceConfig, 'eject.enabled', true)

    await interaction.followUp({
      content: 'CVC Eject service has been enabled.',
      ephemeral: true
    })

    logger.info(
      `CVC Eject service enabled for continent ${instanceConfig.continent}`
    )
  } catch (error) {
    logger.error('Error enabling eject service:', error)
    await interaction.followUp({
      content: 'An error occurred while enabling the eject service.',
      ephemeral: true
    })
  }
}

export async function handleEjectDisable(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
): Promise<void> {
  try {
    await interaction.deferUpdate()

    const ejectService = getEjectService(instanceConfig)
    ejectService.setClient(interaction.client)
    ejectService.stopListening()

    instanceConfig.eject.enabled = false
    await setSettings(instanceConfig, 'eject.enabled', false)

    await interaction.followUp({
      content: 'CVC Eject service has been disabled.',
      ephemeral: true
    })

    logger.info(
      `CVC Eject service disabled for continent ${instanceConfig.continent}`
    )
  } catch (error) {
    logger.error('Error disabling eject service:', error)
    await interaction.followUp({
      content: 'An error occurred while disabling the eject service.',
      ephemeral: true
    })
  }
}