import {
 StringSelectMenuInteraction, EmbedBuilder 
} from 'discord.js'
import { InstanceConfig } from 'interfaces/config.js'
import {
  fetchAllianceMembersRank,
  changeRank
} from 'services/alliance/rank-service.js'
import { logger } from 'utils/logging/logger.js'

export async function handleAllianceRankChange(
  interaction: StringSelectMenuInteraction,
  instanceConfig: InstanceConfig
): Promise<void> {
  const kingdomId = interaction.customId.split('-')[2]
  const newRank = interaction.values[0]

  const rankedMembers = await fetchAllianceMembersRank(instanceConfig)
  const allMembers = Object.values(rankedMembers).flat()
  const member = allMembers.find(m => m.kingdomId === kingdomId)

  if (!member) {
    await interaction.reply({
      content: 'Selected member not found.',
      ephemeral: true
    })
    return
  }

  try {
    const result = await changeRank(instanceConfig, newRank, kingdomId)

    if (result.success) {
      const embed = new EmbedBuilder()
        .setColor('#00FF00')
        .setTitle('Rank Changed Successfully')
        .setDescription(
          `${member.name}'s rank has been changed to Rank ${newRank}.`
        )
        .setTimestamp()

      await interaction.update({
        content: ' ',
        embeds: [embed],
        components: []
      })
    } else {
      await interaction.update({
        content: `Failed to change rank: ${result.message || 'Unknown error'}`,
        components: []
      })
    }
  } catch (error) {
    logger.error(
      `Error changing rank: ${error instanceof Error ? error.message : String(error)}`
    )
    await interaction.reply({
      content: "An error occurred while changing the member's rank.",
      ephemeral: true
    })
  }
}
