import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  type ChatInputCommandInteraction
} from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'
import { assignTitle } from '../../services/title/service.js'
import { BotError } from '../../utils/common/error.js'
import { MIN } from '../../utils/time/constants.js'

export class HandleTitle extends BaseCommandHandler {
  constructor(
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap: TitleToggleMap,
    roleStatusMap: RoleStatusMap
  ) {
    super(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap
    )
  }

  async execute(): Promise<void> {
    await this.ensureToken()

    const role = this.interaction.options.getString('role', true)
    const ueid = this.interaction.options.getString('ueid', true)

    if ((role !== 'Alchemist' && role !== 'Architect') || ueid.length !== 24) {
      await this.interaction.reply({
        content: 'Invalid input. Please check the role and UEID.',
        ephemeral: true
      })
      return
    }

    if (this.titleToggleMap[this.instanceConfig.continent] !== true) {
      await this.interaction.reply({
        content: `Title requests are currently disabled for Continent ${this.instanceConfig.continent}.`,
        ephemeral: true
      })
      return
    }

    let roleStatus = this.roleStatusMap[this.instanceConfig.continent]
    if (!roleStatus) {
      roleStatus = { hasRole: false, roleName: '', expirationTime: undefined }
      this.roleStatusMap[this.instanceConfig.continent] = roleStatus
    }

    const currentTime = Date.now()
    if (
      roleStatus.hasRole &&
      roleStatus.expirationTime &&
      roleStatus.expirationTime > currentTime
    ) {
      const remainingTime = Math.round(
        (roleStatus.expirationTime - currentTime) / MIN
      )
      await this.interaction.reply({
        content: `The ${roleStatus.roleName} role is currently assigned and will be available in ${remainingTime} minutes unless expired early.`,
        ephemeral: false
      })
      return
    }

    await this.interaction.deferReply({ ephemeral: false })

    try {
      await this.ensureToken()
      const result = await assignTitle(
        role,
        ueid,
        this.instanceConfig,
        this.roleStatusMap
      )

      if (result.result) {
        roleStatus = this.roleStatusMap[this.instanceConfig.continent] // Update roleStatus after assignTitle
        const expirationTime = roleStatus.expirationTime
        const formattedExpirationTime = expirationTime
          ? new Date(expirationTime).toLocaleTimeString()
          : 'N/A'

        this.logger.info(
          `[HandleTitle] RoleStatus Change for Continent ${this.instanceConfig.continent}: ${JSON.stringify(roleStatus)}`
        )

        const confirmButton = new ButtonBuilder()
          .setCustomId(`expire-${role}`)
          .setLabel('Expire Role')
          .setStyle(ButtonStyle.Success)

        const actionRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
          confirmButton
        )

        await this.interaction.editReply({
          content: `**${role}** has been assigned until ${formattedExpirationTime}. Use "Expire Role" when done.`,
          components: [actionRow]
        })

        this.logger.info(
          `[HandleTitle] Role ${role} assigned to ${ueid}. Expiration: ${formattedExpirationTime}`
        )
      } else {
        throw new Error(result.message || 'Failed to assign title.')
      }
    } catch (error) {
      if (error instanceof BotError && error.code === 'not_king') {
        await this.interaction.editReply({
          content:
            '❌ Title Request Failed: Account does not have required permissions, try again later',
          components: []
        })
        return
      }
      await this.handleError(
        error,
        'An error occurred while processing the title assignment.'
      )
    }
  }
}

export default {
  data: {
    name: 'request_title',
    description: 'Assign a title to a player',
    options: [
      {
        name: 'role',
        description: 'The role to assign (Alchemist or Architect)',
        type: 3,
        required: true,
        choices: [
          { name: 'Alchemist', value: 'Alchemist' },
          { name: 'Architect', value: 'Architect' }
        ]
      },
      {
        name: 'ueid',
        description: 'The UEID of the player',
        type: 3,
        required: true
      }
    ]
  },
  CommandHandlerClass: HandleTitle
}
