import { EmbedBuilder } from 'discord.js'

import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import type { PlayerProfile } from '../../interfaces/player.js'
import { getPlayerProfiles } from '../../services/storage/user-data-storage.js'

export class HandleSearch extends BaseCommandHandler {
  async execute(): Promise<void> {
    try {
      const user = this.interaction.options.getUser('username', true)
      const member = await this.interaction.guild?.members
        .fetch(user.id)
        .catch(() => null)

      if (!member) {
        await this.interaction.reply({
          content: `User ${user.username} not found in the server.`,
          ephemeral: false
        })
        return
      }

      const profiles = await getPlayerProfiles(this.instanceConfig, member.id)

      if (profiles.length === 0) {
        await this.interaction.reply({
          content: `No data found for user ${user.displayName}.`,
          ephemeral: false
        })
        return
      }

      const embeds = this.createEmbeds(profiles, user.displayName)

      await this.interaction.reply({ embeds, ephemeral: false })
    } catch (error) {
      await this.handleError(
        error,
        'An unexpected error occurred while searching for player data.'
      )
    }
  }

  private createEmbeds(
    profiles: PlayerProfile[],
    displayName: string
  ): EmbedBuilder[] {
    return profiles.map((profile, index) => {
      return new EmbedBuilder()
        .setTitle(
          `👥 **__Player Data for ${displayName} - Account ${index + 1}__**`
        )
        .setColor(0xadd8e6)
        .addFields(
          {
            name: '🏰 Kingdom Name',
            value: profile.name || 'N/A',
            inline: false
          },
          {
            name: '💪 Power',
            value: this.formatNumber(profile.power),
            inline: true
          },
          {
            name: '⚔️ Kill',
            value: this.formatNumber(profile.kill),
            inline: true
          },
          {
            name: '💀 Death',
            value: this.formatNumber(profile.death),
            inline: true
          },
          {
            name: '🏅 Individual Rank',
            value: this.formatNumber(profile.individualRank),
            inline: true
          },
          {
            name: '🌎 Continent Rank',
            value: this.formatNumber(profile.continentRank),
            inline: true
          },
          {
            name: '🏆 Total Points',
            value: this.formatNumber(profile.individualPoints),
            inline: true
          }
        )
        .setFooter({ text: `Generated: ${new Date().toLocaleDateString()}` })
    })
  }

  private formatNumber(value: number | string | undefined | null): string {
    if (value === undefined || value === null) {
      return 'N/A'
    }
    if (typeof value === 'string') {
      return Number.parseInt(value, 10).toLocaleString()
    }
    return value.toLocaleString()
  }
}

export default {
  data: {
    name: 'search',
    description: 'Search for player data',
    options: [
      {
        name: 'username',
        description: 'The username to search for',
        type: 6,
        required: true
      }
    ]
  },
  CommandHandlerClass: HandleSearch
}
