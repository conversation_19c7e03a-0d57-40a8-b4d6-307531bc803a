import { ChatInputCommandInteraction } from 'discord.js'
import { BaseCommandHandler } from 'handlers/commands/command-handler.js'
import { InstanceConfig } from 'interfaces/config.js'

import { logger } from '../logging/logger.js'

import { validateRoleOrReply } from './role-utils.js'

export async function verifyCommandChannel(
  interaction: ChatInputCommandInteraction,
  instanceConfig: InstanceConfig,
  commandName: string
): Promise<boolean> {
  let targetChannelId: string | undefined

  if (
    instanceConfig[commandName as keyof InstanceConfig] &&
    typeof instanceConfig[commandName as keyof InstanceConfig] === 'object' &&
    'channelId' in
      (instanceConfig[commandName as keyof InstanceConfig] as object)
  ) {
    targetChannelId = (
      instanceConfig[commandName as keyof InstanceConfig] as {
        channelId: string
      }
    ).channelId
  } else if (`${commandName}ChannelId` in instanceConfig) {
    targetChannelId = instanceConfig[
      `${commandName}ChannelId` as keyof InstanceConfig
    ] as string
  }

  logger.debug(
    `Verifying channel for ${commandName}: Interaction channelId: ${interaction.channelId}, Target channelId: ${targetChannelId}`
  )

  if (!targetChannelId) {
    logger.warn(`No channel configuration found for command: ${commandName}`)
    return true
  }

  if (interaction.channelId !== targetChannelId) {
    await interaction.reply({
      content: `This command can only be used in the specified channel.`,
      ephemeral: true
    })
    return false
  }

  return true
}

export class CommandUtils {
  static async validateRole(
    interaction: ChatInputCommandInteraction,
    roleName: string
  ): Promise<boolean> {
    const hasRole = await validateRoleOrReply(interaction, roleName)
    if (!hasRole) return false

    return true
  }

  static async handleCommandError(
    handler: BaseCommandHandler,
    error: unknown,
    customMessage: string
  ): Promise<void> {
    logger.error(`Error in ${handler.constructor.name}:`, error)

    // Log the error but don't call handler.handleError to avoid circular reference
    try {
      const interaction = handler.interaction
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: customMessage || 'An error occurred while executing this command.',
          ephemeral: true
        })
      } else if (!interaction.replied) {
        await interaction.editReply({
          content: customMessage || 'An error occurred while executing this command.'
        })
      }
    } catch (replyError) {
      logger.error('Failed to send error response:', replyError)
    }
  }
}
