import {
  type ChatInputCommandInteraction,
  type <PERSON>Row<PERSON>uilder,
  type <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ButtonStyle
} from 'discord.js'

import type {
 RoleStatusMap, TitleToggleMap
} from 'services/title/types.js'

import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'
import { updateEjectPanelEmbed } from '../../services/monitor/eject/utils/eject-panel-embed.js'
import { SocketService } from 'services/socket/service.js'
import { setSettings } from '../../utils/config/config-utils.js'

export class HandleEjectPanel extends BaseCommandHandler {
  constructor(
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap: TitleToggleMap,
    roleStatusMap: RoleStatusMap,
    socketInstance: SocketService,
  ) {
    super(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap,
      socketInstance
    )
  }

  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return

    await this.ensureToken()

    await this.interaction.deferReply({ ephemeral: true })

    const actionRow = this.createEjectPanelButtons()

    try {
      // Create the initial message with just the buttons
      const message = await this.interaction.channel?.send({
        components: [actionRow]
      })

      if (message) {
        // Update the config with the message and channel IDs
        this.instanceConfig.eject.messageId = message.id
        this.instanceConfig.eject.channelId = message.channel.id

        // Save the settings
        await setSettings(this.instanceConfig, 'eject.messageId', message.id)
        await setSettings(this.instanceConfig, 'eject.channelId', message.channel.id)

        // Initialize the eject panel with default settings
        if (this.instanceConfig.eject.enabled === undefined) {
          this.instanceConfig.eject.enabled = false
          await setSettings(this.instanceConfig, 'eject.enabled', false)
        }

        // Update the panel with the embed
        await updateEjectPanelEmbed(
          this.interaction.client,
          this.instanceConfig
        )

        await this.interaction.editReply({
          content: 'Eject panel has been created and updated.'
        })
      } else {
        throw new Error('Failed to create eject panel message')
      }
    } catch (error) {
      this.logger.error('Error creating eject panel:', error)
      await this.interaction.editReply({
        content: 'Failed to create eject panel.'
      })
    }
  }

  private createEjectPanelButtons(): ActionRowBuilder<ButtonBuilder> {
    return this.messageUtils.createActionRow(
      this.messageUtils.createButton(
        'eject-off',
        'Off',
        ButtonStyle.Primary
      ),
      this.messageUtils.createButton(
        'eject-on',
        'On',
        ButtonStyle.Primary
      )
    )
  }
}

export default {
  data: {
    name: 'create_eject_panel',
    description: 'Create the CvC eject panel'
  },
  CommandHandlerClass: HandleEjectPanel
}
