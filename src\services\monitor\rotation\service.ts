import type {
 Client, TextChannel 
} from 'discord.js'

import type { AllianceMember } from '../../../interfaces/alliance'
import type { InstanceConfig } from '../../../interfaces/config'
import { BotError } from '../../../utils/common/error'
import { MessageUtils } from '../../../utils/discord/message-utils'
import { updateRotatePanel } from './utils/rotate-panel-embed'
import { logger } from '../../../utils/logging/logger'
import {
  kickMember,
  inviteMember,
  fetchAllianceMembersRotate
} from '../../alliance/rotate-service'

export class RotateMonitor {
  private intervalId: NodeJS.Timeout | null = null
  private readonly CHECK_INTERVAL = 30 * 60 * 1000 // 30 minutes

  constructor(
    private readonly client: Client,
    private readonly instanceConfig: InstanceConfig
  ) {}

  async start(): Promise<void> {
    if (this.intervalId) {
      return
    }

    await this.performRotation()

    this.intervalId = setInterval(async () => {
      try {
        await this.performRotation()
      } catch (error) {
        logger.error('Error in rotate monitor:', error)
      }
    }, this.CHECK_INTERVAL)

    logger.info(
      `Started rotate monitor for continent ${this.instanceConfig.continent}`
    )
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
      logger.info(
        `Stopped rotate monitor for continent ${this.instanceConfig.continent}`
      )
    }
  }

  private async performRotation(): Promise<void> {
    const rotateConfig = this.instanceConfig.rotate
    if (!rotateConfig || !rotateConfig.enabled) {
      return
    }

    try {
      const members = await fetchAllianceMembersRotate(this.instanceConfig)
      const inactiveMembers = this.findInactiveMembers(
        members,
        rotateConfig.kickThreshold
      )
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const kickCount = await this.kickInactiveMembers(
        inactiveMembers,
        rotateConfig.maxKick
      )
      await updateRotatePanel(this.client, this.instanceConfig)
    } catch (error) {
      this.handleRotateMonitorError(error)
    }
  }

  private findInactiveMembers(
    members: AllianceMember[],
    kickThreshold: number
  ): AllianceMember[] {
    const now = Date.now()
    return members.filter(member => {
      if (!member.lastLogined) {
        return false
      }
      const lastOnline = new Date(member.lastLogined).getTime()
      const inactiveTime = now - lastOnline
      const daysInactive = inactiveTime / (1000 * 60 * 60 * 24)
      return daysInactive >= kickThreshold
    })
  }

  private async kickInactiveMembers(
    inactiveMembers: AllianceMember[],
    maxKick: number
  ): Promise<number> {
    let kickCount = 0
    for (const member of inactiveMembers) {
      if (!member?.kingdomId) {
        logger.warn(`Skipping invalid member data: ${JSON.stringify(member)}`)
        continue
      }

      if (kickCount >= maxKick) break

      const kicked = await kickMember(this.instanceConfig, member.kingdomId)
      if (kicked) {
        kickCount++
        try {
          await this.sendKickEmbed(member)
          await inviteMember(this.instanceConfig, member.kingdomId)
        } catch (error) {
          logger.error(
            `Error sending kick embed or reinviting member ${member.kingdomId}:`,
            error
          )
        }
      }
    }
    return kickCount
  }

  private async sendKickEmbed(member: AllianceMember): Promise<void> {
    if (!this.instanceConfig.rotate?.alertChannelId) {
      logger.warn('Rotate channel not configured. Skipping kick notification.')
      return
    }

    const channel = (await this.client.channels.fetch(
      this.instanceConfig.rotate.alertChannelId
    )) as TextChannel
    if (!channel || !channel.isTextBased()) {
      logger.error('Invalid rotate channel configuration.')
      return
    }

    const embed = MessageUtils.createEmbed(
      'Member Kicked and Re-invited',
      `${member.name || 'Unknown Member'} has been kicked due to inactivity and re-invited.`,
      [
        {
          name: 'Power',
          value: member.power?.toString() || 'Unknown',
          inline: true
        },
        {
          name: 'Rank',
          value: member.rank?.toString() || 'Unknown',
          inline: true
        }
      ]
    )

    await channel.send({ embeds: [embed] })
  }

  private handleRotateMonitorError(error: unknown): void {
    if (error instanceof BotError) {
      logger.error(
        `Rotate monitor BotError for Continent ${this.instanceConfig.continent}:`,
        error.message,
        error.code
      )
    } else {
      logger.error(
        `Rotate monitor error for Continent ${this.instanceConfig.continent}:`,
        error
      )
    }
  }
}
