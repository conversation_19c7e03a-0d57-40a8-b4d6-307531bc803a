import type { Client } from 'discord.js'
import { StakerService } from 'services/staker/service.js'

import type { InstanceConfig } from '../../../interfaces/config.js'
import { Monitor } from '../../../utils/common/monitor.js'
import { logger } from '../../../utils/logging/logger.js'

export class StakerMonitor {
  private monitor: Monitor
  private stakerService: StakerService

  constructor(
    private client: Client,
    private instanceConfig: InstanceConfig
  ) {
    this.stakerService = new StakerService(instanceConfig)
    this.monitor = new Monitor(instanceConfig, {
      interval: 24 * 60 * 60 * 1000, // 24 hours
      onTick: this.updateStakerInfo.bind(this),
      onError: this.handleError.bind(this)
    })
  }

  start(): void {
    this.monitor.start()
    logger.info(
      `Staker monitor started for Continent ${this.instanceConfig.continent}`
    )
  }

  stop(): void {
    this.monitor.stop()
    logger.info(
      `Staker monitor stopped for Continent ${this.instanceConfig.continent}`
    )
  }

  private async updateStakerInfo(): Promise<void> {
    try {
      await this.stakerService.updateStakerInformation(this.client)
      logger.info(
        `Updated staker information for Continent ${this.instanceConfig.continent}`
      )
    } catch (error) {
      this.handleError(error)
    }
  }

  private handleError(error: unknown): void {
    logger.error(
      `Error in staker monitor for Continent ${this.instanceConfig.continent}:`,
      error
    )
  }
}
