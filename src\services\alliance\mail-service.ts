import type { InstanceConfig } from '../../interfaces/config.js'
import { BotError } from '../../utils/common/error.js'
import { getApiInstance } from '../api/api-instance.js'

interface AllianceInfoResponse {
  alliance: {
    _id: string
  }
}

interface MailResponse {
  result: boolean
}

export async function sendMailToAlliance(
  instanceConfig: InstanceConfig,
  subject: string,
  content: string
): Promise<{ success: boolean }> {
  const api = getApiInstance(instanceConfig.continent)

  try {
    const allianceInfoResponse = await api.post<AllianceInfoResponse>(
      '/alliance/info/my',
      {}
    )
    const allianceId = allianceInfoResponse.alliance._id

    const mailData = {
      allianceId: allianceId,
      subject: subject,
      content: content
    }

    const mailResponse = await api.post<MailResponse>('/mail/send', mailData)

    if (mailResponse.result) {
      return { success: true }
    } else {
      return { success: false }
    }
  } catch (error) {
    throw new BotError(
      `Failed to send mail: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'MAIL_SEND_ERROR'
    )
  }
}

export async function sendMail(
  instanceConfig: InstanceConfig,
  targetName: string,
  toIds: string
): Promise<{ success: boolean }> {
  const api = getApiInstance(instanceConfig.continent)

  try {
    const mailData = {
      toIds,
      subject: 'Application Status',
      content: `Your application was denied for ${targetName} as your power does not meet the current minimum power of ${instanceConfig.manage.minPower}.\n\n`
    }

    const mailResponse = await api.post<MailResponse>('/mail/send', mailData)

    if (mailResponse.result) {
      return { success: true }
    } else {
      return { success: false }
    }
  } catch (error) {
    throw new BotError(
      `Failed to send mail: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'MAIL_SEND_ERROR'
    )
  }
}
