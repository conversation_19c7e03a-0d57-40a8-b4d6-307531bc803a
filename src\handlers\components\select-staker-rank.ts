import type { StringSelectMenuInteraction } from 'discord.js'
import * as ethers from 'ethers'
const { formatUnits } = ethers

import type { InstanceConfig } from '../../interfaces/config.js'
import { StakerService } from '../../services/staker/service.js'
import { validateRoleOrReply } from '../../utils/discord/role-utils.js'
import { createStakerEmbed } from '../../services/staker/utils/staker-panel-embed.js'
import { logger } from '../../utils/logging/logger.js'

export async function handleRankVerification(
  interaction: StringSelectMenuInteraction,
  instanceConfig: InstanceConfig
): Promise<void> {
  await interaction.deferUpdate()
  const stakerService = new StakerService(instanceConfig)

  if (
    !(await validateRoleOrReply(interaction, instanceConfig.staker.pledgerRole))
  ) {
    return
  }

  const selectedAddress = interaction.values[0]
  const member = interaction.member

  if (!member) {
    await interaction.editReply({
      content: 'Could not fetch member information.'
    })
    return
  }

  const stakers = await stakerService.fetchVerifiedStakers()
  const stakerToUpdate = stakers.find(
    staker => staker.address.toLowerCase() === selectedAddress.toLowerCase()
  )

  if (!stakerToUpdate) {
    await interaction.editReply({
      content: 'Selected staker not found.'
    })
    return
  }

  if (stakerToUpdate.comment) {
    await interaction.editReply({
      content: 'This rank has already been verified.'
    })
    return
  }

  stakerToUpdate.comment = member.user.username
  await stakerService.updateVerifiedStakers(stakers)

  logger.info(
    `User ${member.user.username} verified rank for address ${selectedAddress}`
  )

  const totalLOKA = stakerService.calculateTotalLOKA(stakers)
  const totalLOKAFormatted = formatUnits(totalLOKA, 18)

  const { embed, components, attachment } = createStakerEmbed(
    stakers,
    totalLOKAFormatted,
    instanceConfig.continent
  )

  await interaction.editReply({
    embeds: [embed],
    components: components,
    files: [attachment]
  })
}
