import {
 <PERSON><PERSON><PERSON><PERSON>, Role 
} from 'discord.js'

import { BotError } from '../../utils/common/error.js'

export async function addRoleToMember(
  member: GuildMember,
  role: Role
): Promise<void> {
  try {
    await member.roles.add(role)
  } catch (error) {
    throw new BotError(
      `Failed to add role '${role.name}' to member '${member.displayName}'`,
      'ROLE_ASSIGNMENT_ERROR'
    )
  }
}
