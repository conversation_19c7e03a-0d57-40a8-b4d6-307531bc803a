import { EventEmitter } from "events"

import type { InstanceConfig } from "../../interfaces/config.js"
import {
 b64xorEnc, parseData
} from "../../utils/decode.js"
import { logger } from "../../utils/logging/logger.js"

import { SocketConnection } from "./connection/socket-connection.js"
import {
 type SocketType, type MessageHandler, type SocketOptions, ConnectionState
} from "./types.js"
import {
 MessageRouter, MessageCategories
} from "./utils/message-router.js"

const DEFAULT_OPTIONS: Required<SocketOptions> = {
  pingInterval: 25000,
  reconnectDelay: 5000,
  maxReconnectAttempts: 3,
  connectionTimeout: 10000,
  debug: false,
}

export class SocketService extends EventEmitter {
  private readonly options: Required<SocketOptions>
  private readonly connections = new Map<SocketType, SocketConnection>()
  private readonly enteredZones = new Set<number>()
  private readonly messageRouter = new MessageRouter()

  constructor(
    private readonly instanceConfig: InstanceConfig,
    options?: SocketOptions,
  ) {
    super()
    this.setMaxListeners(50)
    this.options = { ...DEFAULT_OPTIONS, ...options }
  }

  public async initialize(types: SocketType[]): Promise<void> {
    if (!this.instanceConfig.kingdomData?.networks) {
      throw new Error("Network URLs not available. Call kingdom/enter first")
    }

    for (let i = 0; i < types.length; i++) {
      const type = types[i]

      try {
        await this.connectSocket(type)

        if (i < types.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 200))
        }
      } catch (error) {
        logger.error(`[SocketService] Failed to initialize ${type}: ${error}`)

        for (let j = 0; j < i; j++) {
          this.closeSocket(types[j])
        }

        throw error
      }
    }
  }

  private async connectSocket(type: SocketType): Promise<void> {
    let connection = this.connections.get(type)

    if (!connection) {
      const url = this.getSocketUrl(type)
      connection = new SocketConnection(
        type,
        url,
        this.options,
        this.handleMessage.bind(this, type),
        this.handleStateChange.bind(this, type),
      )
      this.connections.set(type, connection)
    }

    if (connection.getState() !== ConnectionState.CONNECTED && connection.getState() !== ConnectionState.INITIALIZED) {
      let retryCount = 0;
      const MAX_CONNECT_RETRIES = 3;

      while (retryCount < MAX_CONNECT_RETRIES) {
        try {
          await connection.connect();
          return;
        } catch (error) {
          retryCount++;

          if (retryCount >= MAX_CONNECT_RETRIES) {
            logger.error(`[SocketService] All connection attempts failed for ${type}`);
            throw error;
          }

          const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
          logger.info(`[SocketService] Retrying connection for ${type} in ${delay}ms`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
  }

  private getSocketUrl(type: SocketType): string {
    const networks = this.instanceConfig.kingdomData!.networks
    let baseUrl: string

    switch (type) {
      case "sock":
        baseUrl = networks.kingdoms[0]
        break
      case "socc":
        baseUrl = networks.chats[0]
        break
      case "socf":
        baseUrl = networks.fields[0]
        break
      default:
        throw new Error(`Unknown socket type: ${type}`)
    }

    const baseQueryParams = "EIO=4&transport=websocket"
    return type === "socc"
      ? `${baseUrl}?${baseQueryParams}`
      : `${baseUrl}?${baseQueryParams}&token=${this.instanceConfig.token}`
  }

  private handleStateChange(type: SocketType, state: ConnectionState) {
    logger.debug(`[SocketService] ${type} state changed to ${state}`)

    if (state === ConnectionState.INITIALIZED) {
      switch (type) {
        case "sock":
          this.send(type, "/kingdom/enter", { token: this.instanceConfig.token })
          break

        case "socc":
          this.send(type, "/chat/enter", { token: this.instanceConfig.token })
          break

        case "socf":
          if (!this.instanceConfig.kingdomData) {
            logger.error("[SocketService] Cannot initialize field socket: missing kingdom data")
            return
          }

          const kingdomData = this.instanceConfig.kingdomData
          if (!kingdomData || !kingdomData.kingdom || !kingdomData.kingdom.loc) {
            logger.error("[SocketService] Cannot calculate zones: Invalid kingdom data")
            return
          }

          const loc = kingdomData.kingdom.loc as unknown as number[]

          const continent = loc[0]
          const x = loc[1] // Second element is x
          const y = loc[2] // Third element is y

          logger.debug(`[SocketService] Using location: continent=${continent}, x=${x}, y=${y}`)

          this.send(type, "/field/enter/v3", { token: this.instanceConfig.token }, true)

          this.send(type, "/zone/leave/list/v2", {
            world: continent,
            zones: "[]",
          })

          const { getZone } = require("../../utils/zone/zone-util.js");
          const centerZone = getZone(x, y);

          const initialZones = [centerZone - 1, centerZone, centerZone + 1];

          this.send(
            type,
            "/zone/enter/list/v4",
            {
              world: continent,
              zones: JSON.stringify(initialZones),
              compType: 3,
            },
            true,
          )

          setTimeout(() => {
            if (!this.isConnected(type)) return

            this.send(type, "/zone/leave/list/v2", {
              world: continent,
              zones: JSON.stringify(initialZones),
            })

            const { getZones } = require("../../utils/zone/zone-util.js");
            const zoneResult = getZones(x, y);
            const additionalZones = zoneResult.zones;
            this.send(
              type,
              "/zone/enter/list/v4",
              {
                world: continent,
                zones: JSON.stringify(additionalZones),
                compType: 3,
              },
              true,
            )

            // Set up periodic field refresh to keep the connection active
            const FIELD_REFRESH_INTERVAL = 60000; // 1 minute
            logger.info(`[SocketService] Setting up periodic field refresh every ${FIELD_REFRESH_INTERVAL/1000} seconds`)

            setInterval(() => {
              if (!this.isConnected(type)) return

              logger.debug(`[SocketService] Performing periodic field refresh`)

              // Refresh zone data to keep connection active
              this.send(
                type,
                "/zone/enter/list/v4",
                {
                  world: continent,
                  zones: JSON.stringify(additionalZones),
                  compType: 3,
                },
                true,
              )
            }, FIELD_REFRESH_INTERVAL)
          }, 1000)
          break
      }
    }
  }

  private handleMessage(type: SocketType, path: string, payload: unknown) {
    if (MessageCategories.IGNORED.has(path)) {
      return
    }

    // Only log detailed info for field/object/update messages
    if (path === "/field/object/update") {
      logger.info(`[FIELD_OBJECT_UPDATE] SocketService received raw message: ${JSON.stringify(payload)}`)
    } else {
      logger.debug(`[SocketService] Received ${type}:${path} message`)
    }

    let processedPayload: unknown = payload

    if (MessageCategories.ENCODED.has(path)) {
      try {
        processedPayload = parseData(payload, this.instanceConfig.xorKey)

        // Log the exact decoded data for field/object/update messages
        if (path === "/field/object/update") {
          logger.info(`[FIELD_OBJECT_UPDATE] SocketService decoded message: ${JSON.stringify(processedPayload)}`)
        }
      } catch (error) {
        if (path === "/field/object/update") {
          logger.error(`[FIELD_OBJECT_UPDATE] SocketService error decoding message: ${error}`)
        } else {
          logger.error(`[SocketService] Error decoding message for ${path}: ${error}`)
        }
        processedPayload = payload
      }
    }

    const handled = this.messageRouter.route(path, processedPayload)

    if (!handled) {
      const eventName = `message:${type}:${path}`
      if (this.listenerCount(eventName) > 0) {
        if (path === "/field/object/update") {
          logger.info(`[FIELD_OBJECT_UPDATE] SocketService emitting event to ${this.listenerCount(eventName)} listeners`)
        } else {
          logger.debug(`[SocketService] Emitting ${eventName} event`)
        }
        this.emit(eventName, processedPayload)
      } else if (path === "/field/object/update") {
        logger.warn(`[FIELD_OBJECT_UPDATE] SocketService has NO LISTENERS for this event!`)
      }
    } else if (path === "/field/object/update") {
      logger.info(`[FIELD_OBJECT_UPDATE] SocketService message was handled by the router`)
    }
  }

  private closeSocket(type: SocketType) {
    const connection = this.connections.get(type)
    if (!connection) return

    if (connection.getState() === ConnectionState.CONNECTED || connection.getState() === ConnectionState.INITIALIZED) {
      if (type === "socf") {
        this.send(type, "/field/leave", { token: this.instanceConfig.token })
      }
    }

    connection.close()
    this.connections.delete(type)
  }

  public send(type: SocketType, path: string, data: unknown, needsEncryption = false): boolean {
    const connection = this.connections.get(type)

    if (!connection || !connection.isReady()) {
      logger.warn(`[SocketService] Cannot send to ${type}: not connected`)
      return false
    }

    try {
      const payload = needsEncryption ? b64xorEnc(this.instanceConfig.xorKey, data) : data

      const message = `42${JSON.stringify([path, payload])}`

      logger.debug(`[SocketService] Sending to ${type}:${path}`)
      connection.sendRaw(message)
      return true
    } catch (error) {
      logger.error(`[SocketService] Failed to send message to ${type}:${path}: ${error}`)
      return false
    }
  }

  public onMessage<T = unknown>(type: SocketType, path: string, handler: MessageHandler<T>) {
    const eventName = `message:${type}:${path}`
    logger.info(`[SocketService] Registering handler for ${eventName}`)

    // Create a wrapper that logs when the handler is called
    const wrappedHandler = (data: T) => {
      logger.debug(`[SocketService] Handler for ${eventName} called with data: ${JSON.stringify(data).substring(0, 200)}${JSON.stringify(data).length > 200 ? '...' : ''}`)
      return handler(data)
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    this.on(eventName, wrappedHandler as any)

    // Log the number of listeners
    const listenerCount = this.listenerCount(eventName)
    logger.debug(`[SocketService] Now have ${listenerCount} listeners for ${eventName}`)
  }

  public offMessage(type: SocketType, path: string, handler?: (data: unknown) => void) {
    const eventName = `message:${type}:${path}`
    if (handler) {
      logger.debug(`[SocketService] Removing specific handler for ${eventName}`)
      this.off(eventName, handler)
    } else {
      logger.debug(`[SocketService] Removing all handlers for ${eventName}`)
      this.removeAllListeners(eventName)
    }

    // Log the number of listeners
    const listenerCount = this.listenerCount(eventName)
    logger.debug(`[SocketService] Now have ${listenerCount} listeners for ${eventName}`)
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public registerHandler(path: string, handler: (data: any) => void) {
    logger.info(`[SocketService] Registering router handler for path: ${path}`)
    this.messageRouter.register(path, handler)
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public unregisterHandler(path: string, handler?: (data: any) => void) {
    logger.debug(`[SocketService] Unregistering router handler for path: ${path}`)
    this.messageRouter.unregister(path, handler)
  }

  public close() {

    for (const [type, connection] of this.connections.entries()) {
      if (type === "socf" && connection.isReady()) {
        this.send(type, "/field/leave", { token: this.instanceConfig.token })
      }
      connection.close()
    }

    this.connections.clear()
    this.enteredZones.clear()
    this.removeAllListeners()
  }

  public isConnected(type?: SocketType): boolean {
    if (type) {
      const connection = this.connections.get(type)
      return !!connection && connection.isReady()
    }

    if (this.connections.size === 0) return false

    return Array.from(this.connections.values()).every((conn) => conn.isReady())
  }

  public areSocketsInitialized(types: SocketType[]): boolean {
    return types.every((type) => {
      const connection = this.connections.get(type)
      return !!connection && connection.getState() === ConnectionState.INITIALIZED
    })
  }
}