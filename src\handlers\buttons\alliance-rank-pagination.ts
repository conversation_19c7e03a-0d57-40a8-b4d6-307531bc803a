import {
  type ButtonInteraction,
  StringSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
  ActionRowBuilder
} from 'discord.js'
import type { InstanceConfig } from 'interfaces/config.js'
import { fetchAllianceMembersRank } from 'services/alliance/rank-service.js'

export async function handleAllianceRankPagination(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig
): Promise<void> {
  const direction = interaction.customId === 'alliance-next' ? 1 : -1
  const rankedMembers = await fetchAllianceMembersRank(instanceConfig)
  const allMembers = Object.values(rankedMembers).flat()

  const MEMBERS_PER_PAGE = 24
  const totalPages = Math.ceil(allMembers.length / MEMBERS_PER_PAGE)

  const currentPageMatch =
    interaction.message.content.match(/Page (\d+)\/(\d+)/)
  let currentPage = currentPageMatch
    ? Number.parseInt(currentPageMatch[1], 10) - 1
    : 0

  currentPage = Math.max(0, Math.min(currentPage + direction, totalPages - 1))

  const pageMembers = allMembers.slice(
    currentPage * MEMBERS_PER_PAGE,
    (currentPage + 1) * MEMBERS_PER_PAGE
  )

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId('select-member')
    .setPlaceholder('Select a member')
    .addOptions(
      pageMembers.map(member => ({
        label: `${member.name} (Rank ${member.rank})`,
        value: `${member.kingdomId}|${member.rank}`
      }))
    )

  const buttons = [
    new ButtonBuilder()
      .setCustomId('alliance-prev')
      .setLabel('Previous')
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(currentPage === 0),
    new ButtonBuilder()
      .setCustomId('alliance-next')
      .setLabel('Next')
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(currentPage === totalPages - 1)
  ]

  const actionRows = [
    new ActionRowBuilder<StringSelectMenuBuilder>().addComponents(selectMenu),
    new ActionRowBuilder<ButtonBuilder>().addComponents(buttons)
  ]

  await interaction.update({
    content: `Select a member to change their rank (Page ${currentPage + 1}/${totalPages}):`,
    components: actionRows
  })
}
