import type {
  AllianceMember,
  AllianceMembersResponse
} from '../../interfaces/alliance.js'
import type { InstanceConfig } from '../../interfaces/config.js'
import { BotError } from '../../utils/common/error.js'
import { getApiInstance } from '../api/api-instance.js'

interface RankedMembers {
  rank1: AllianceMember[]
  rank2: AllianceMember[]
  rank3: AllianceMember[]
  rank4: AllianceMember[]
}

export async function fetchAllianceMembersRank(
  instanceConfig: InstanceConfig
): Promise<RankedMembers> {
  try {
    const api = getApiInstance(instanceConfig.continent)
    const response = await api.post<AllianceMembersResponse>(
      '/alliance/members/list',
      { allianceId: '' }
    )

    if (!response || !response.members) {
      return { rank1: [], rank2: [], rank3: [], rank4: [] }
    }

    const rankedMembers: RankedMembers = {
      rank1: [],
      rank2: [],
      rank3: [],
      rank4: []
    }

    response.members.forEach(group => {
      if (group.members) {
        group.members.forEach(member => {
          const simplifiedMember: AllianceMember = {
            kingdomId: member.kingdomId || '',
            name: member.name,
            rank: member.rank || 1,
            lastLogined: member.lastLogined || new Date(),
            logined: member.logined || false
          }
          switch (simplifiedMember.rank) {
            case 1:
              rankedMembers.rank1.push(simplifiedMember)
              break
            case 2:
              rankedMembers.rank2.push(simplifiedMember)
              break
            case 3:
              rankedMembers.rank3.push(simplifiedMember)
              break
            case 4:
              rankedMembers.rank4.push(simplifiedMember)
              break
          }
        })
      }
    })

    return rankedMembers
  } catch (error) {
    throw new BotError(
      'Failed to fetch alliance members',
      'FETCH_MEMBERS_ERROR'
    )
  }
}

export async function changeRank(
  instanceConfig: InstanceConfig,
  targetRank: string,
  targetKingdomId: string
) {
  try {
    const api = getApiInstance(instanceConfig.continent)

    const payload = {
      memberKingdomId: targetKingdomId,
      rank: targetRank,
      title: '0'
    }
    const response = await api.post<{ result: boolean }>(
      '/alliance/member/rank',
      payload
    )

    if (!response || !response.result) {
      throw new BotError('Failed to change rank', 'CHANGE_RANK_ERROR')
    }

    return {
      success: true
    }
  } catch (error) {
    return {
      success: false,
      message: `Failed to change rank: ${error instanceof Error ? error.message : String(error)}`
    }
  }
}
