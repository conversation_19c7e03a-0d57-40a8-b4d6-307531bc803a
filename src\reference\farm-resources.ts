import { mapSearch } from '../../shared/services/map-search/map-search'
import {
  sendWSMessageToClient,
  watchForData
} from '../../shared/services/web-socket-proxy/web-socket-proxy-server'
import type GameClient from '../game-client'
import { processResources } from '../../shared/auto-farm/process-resources'
import { worldObjectsService } from '../../shared/services/object-service/world-object-service'
import { filterWorldObjects } from '../../shared/services/object-service/filter-world-objects'
import type { ObjectTypeCode } from '../../shared/filters/types'

const WATCH_TIMEOUT = 2500

export async function farmResources(client: GameClient) {
  console.warn('farming resources')

  const [worldId, x, y] = client.kingdomData.loc
  const objectTypes = client.settings.autoFarm
    .split(',')
    .map(type => type.trim()) as ObjectTypeCode[]

  client.logger.info(
    'farmResources command invoked on server side',
    client.kingdomData.loc
  )

  await processResources(client)

  worldObjectsService.removeExpiredObjects(worldId)
  worldObjectsService.removeDuplicateObjects(client, worldId)

  const socketWrap = {
    send: async (socketUrl: string, message: string) => {
      await sendWSMessageToClient(client.kingdomData._id, {
        serverUrl: socketUrl,
        message
      })
    }
  }

  const mapSearchService = mapSearch({
    autoRun: true,
    searchDistance: client.settings.searchDistance,
    token: client.token,
    xorKey: client.xorKey,
    socketWrap,
    kingdom: {
      _id: client.kingdomData._id,
      loc: {
        worldId,
        x,
        y
      }
    }
  })

  while (mapSearchService.isStarted()) {
    await new Promise<void>((resolve, reject) => {
      const timeout = setTimeout(() => {
        client.logger.warn('Timeout reached for watchForData')
        resolve()
      }, WATCH_TIMEOUT)

      watchForData(client.kingdomData._id, 'field/objects/v4', data => {
        try {
          filterWorldObjects(client, data, objectTypes)

          clearTimeout(timeout)
          resolve()
        } catch (error) {
          client.logger.error('Error processing received objects:', error)
          clearTimeout(timeout)
          reject(error)
        }
      })
    })

    await mapSearchService.searchZones(client)

    if (!mapSearchService.isStarted()) {
      client.logger.warn('Search paused. Processing resources.')
      break
    }
  }
  await processResources(client)
  //await client.enterKingdom()
  mapSearchService.resumeSearch(client)

  client.logger.info('farmResources command completed')
  return
}
