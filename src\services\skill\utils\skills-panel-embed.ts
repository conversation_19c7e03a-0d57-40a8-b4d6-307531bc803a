import {
 type Client, EmbedBuilder, type TextChannel 
} from 'discord.js'

import type { InstanceConfig } from '../../../interfaces/config.js'
import {
  fetchSkillData,
  getSkillName
} from '../service.js'
import { logger } from '../../../utils/logging/logger.js'

export async function createSkillsPanel(instanceConfig: InstanceConfig) {
  try {
    const skillData = await fetchSkillData(instanceConfig)
    const currentTime = new Date().getTime()

    const skillFields = skillData.map(skill => {
      const skillName = getSkillName(skill.skillCode)
      const nextSkillTime = new Date(skill.nextSkillTime).getTime()
      const endTime = new Date(skill.endTime).getTime()

      let skillStatus
      if (endTime > currentTime) {
        skillStatus = `Active until ${new Date(endTime).toLocaleString()}`
      } else if (nextSkillTime > currentTime) {
        skillStatus = `Next available at ${new Date(nextSkillTime).toLocaleString()}`
      } else {
        skillStatus = `Available`
      }

      return { name: skillName, value: skillStatus, inline: true }
    })

    const embed = new EmbedBuilder()
      .setColor('#0099FF')
      .setTitle('Global Skills Panel')
      .setDescription(
        'Use the buttons below to activate skills or refresh the status.'
      )
      .addFields(skillFields)
      .setTimestamp()

    return { embeds: [embed] }
  } catch (error) {
    logger.error('Error creating global skills panel embed:', error)
    return {
      embeds: [
        new EmbedBuilder()
          .setColor('#FF0000')
          .setTitle('Error')
          .setDescription(
            'Failed to fetch global skills data. Please try again later.'
          )
      ]
    }
  }
}

export async function updateSkillsPanel(
  client: Client,
  instanceConfig: InstanceConfig
) {
  if (!instanceConfig.skill?.channelId || !instanceConfig.skill?.messageId) {
    logger.error('Global skills panel configuration is missing')
    return
  }

  try {
    const channel = (await client.channels.fetch(
      instanceConfig.skill.channelId
    )) as TextChannel
    if (!channel || !channel.isTextBased()) {
      logger.error('Invalid global skills panel channel configuration')
      return
    }

    const message = await channel.messages.fetch(instanceConfig.skill.messageId)
    const updatedPanel = await createSkillsPanel(instanceConfig)
    await message.edit(updatedPanel)
  } catch (error) {
    logger.error('Error updating global skills panel:', error)
  }
}
