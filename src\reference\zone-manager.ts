import type { Zone } from './types'
import { getZones } from '../../../utils/zone-util'

export class ZoneManager {
  private zones: Record<number, Zone> = {}

  initialize(x: number, y: number): void {
    const baseZones = getZones(x, y)

    baseZones.zones.forEach((zoneId, index) => {
      this.zones[index] = {
        id: zoneId,
        x1: x + ((index % 3) - 1) * 32,
        y1: y + Math.floor(index / 3 - 1) * 32,
        x2: x + ((index % 3) - 1) * 32 + 32,
        y2: y + Math.floor(index / 3 - 1) * 32 + 32
      }
    })

    //client.logger.info('Initialized dynamic zones:', this.zones)
  }

  getZones(): Record<number, Zone> {
    return this.zones
  }
}
