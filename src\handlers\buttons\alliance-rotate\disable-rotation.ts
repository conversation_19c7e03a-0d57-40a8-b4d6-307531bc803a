/* eslint-disable @typescript-eslint/no-unused-vars */
import type { ButtonInteraction } from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types'

import type { InstanceConfig } from '../../../interfaces/config'
import {
  closeAlliance,
  stopRotateMonitor
} from '../../../services/alliance/rotate-service'
import type { ApiService } from '../../../services/api/api-service'
import { setSettings } from '../../../utils/config/config-utils'
import { updateRotatePanel } from '../../../services/monitor/rotation/utils/rotate-panel-embed'
import { logger } from '../../../utils/logging/logger'

export async function handleDisableRotation(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap
): Promise<void> {
  try {
    await interaction.deferUpdate()

    if (!instanceConfig.rotate?.enabled) {
      await interaction.followUp({
        content: 'Alliance rotation is not active.',
        ephemeral: true
      })
      return
    }

    const result = await closeAlliance(instanceConfig)
    if (result.result) {
      instanceConfig.rotate!.enabled = false
      await setSettings(instanceConfig, 'rotate.enabled', false)
      stopRotateMonitor()

      await updateRotatePanel(interaction.client, instanceConfig)

      await interaction.followUp({
        content: 'Alliance rotation has been disabled successfully!',
        ephemeral: true
      })
    } else {
      await interaction.followUp({
        content: result.message,
        ephemeral: true
      })
    }
  } catch (error) {
    logger.error('Error disabling rotation:', error)
    await interaction.followUp({
      content: 'An error occurred while disabling rotation.',
      ephemeral: true
    })
  }
}
