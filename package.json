{"name": "lok-discord-bot", "version": "1.0.0", "description": "Discord bot for League of Kingdoms", "main": "dist/index.js", "type": "module", "scripts": {"start": "node dist/index.js", "build": "node esbuild.config.js", "dev": "node esbuild.config.js --watch", "start:dev": "node dist/index.js", "lint": "eslint . && prettier --check .", "lint:fix": "eslint --fix . && prettier --write .", "test": "vitest run", "test:eject": "tsx src/tests/run-tests.ts", "test:watch": "vitest", "watch": "pnpm run build && pnpm run lint:fix", "watch:build": "nodemon --watch src -e ts,tsx --exec 'pnpm run build'", "watch:lint": "nodemon --watch src -e ts,tsx --exec 'pnpm run lint'", "watch:prettier": "nodemon --watch src -e ts,tsx --exec 'pnpm run prettier:write'", "prettier:check": "prettier --check .", "prettier:write": "prettier --write .", "dev:watch": "concurrently \"pnpm run watch:build\" \"pnpm run watch:lint\" \"pnpm run watch:prettier\""}, "dependencies": {"@discordjs/builders": "^1.7.0", "@fastify/cors": "^10.0.1", "@fastify/static": "^8.0.1", "@fastify/websocket": "^11.0.1", "@types/ws": "^8.5.12", "atob": "^2.1.2", "axios": "^1.7.7", "buffer": "^6.0.3", "csv-parse": "^5.6.0", "csv-stringify": "^6.5.2", "csv-writer": "^1.6.0", "d": "^1.0.2", "date-fns": "^3.3.1", "discord.js": "^14.14.1", "dotenv": "^16.4.4", "ethers": "^6.7.1", "fastify": "^5.0.0", "node-cache": "^5.1.2", "pako": "^2.1.0", "qs": "^6.11.2", "typescript": "^5.6.2", "uuid": "^11.0.5", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.0", "zlib": "^1.0.5"}, "devDependencies": {"@eslint/compat": "^1.1.1", "@eslint/js": "^8.56.0", "@types/node": "^20.16.7", "concurrently": "^8.2.2", "esbuild": "^0.21.5", "esbuild-node-externals": "^1.13.0", "eslint": "~9.11.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "glob": "^10.3.10", "globals": "^15.9.0", "nodemon": "^3.0.3", "prettier": "^3.3.3", "ts-node": "^10.9.2", "tsx": "^4.19.1", "typescript-eslint": "^7.18.0", "vitest": "^1.4.0"}, "packageManager": "pnpm@9.1.1"}