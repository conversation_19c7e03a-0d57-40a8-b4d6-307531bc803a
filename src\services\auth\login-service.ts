import { InstanceConfig } from 'interfaces/config.js'
import { setSettings } from 'utils/config/config-utils.js'
import { decodeRegionHash } from 'utils/decode.js'
import { logger } from 'utils/logging/logger.js'

import { Continent } from '../../interfaces/config.js'
import {
  getApiInstance,
  initializeApi,
  updateParams
} from '../api/api-instance.js'

import { LoginResponse } from './types.js'

export async function login(instanceConfig: InstanceConfig) {
  const continent = instanceConfig.continent
  const payload = {
    authType: "email",
    email: instanceConfig.username,
    password: instanceConfig.password,
    deviceInfo: {
      OS: "Windows 10",
      country: "USA",
      language: "English",
      bundle: "",
      version: "1.1789.164.245",
      platform: "web",
      pushId: "",
      build: "global"
    }
  }

  logger.info(`[loginService] Attempting login for Continent ${continent}`)

  try {
    // Initialize API instance first
    initializeApi('', '', instanceConfig)

    const api = getApiInstance(continent)
    const response = await api.post<LoginResponse>('/auth/login', payload)

    if (!response.token || !response.regionHash) {
      logger.error(
        `[loginService] Missing token or regionHash in response for Continent ${continent}`
      )
      return null
    }

    // Update instance config with token and xorKey
    const xorKey = decodeRegionHash(response.regionHash)
    updateParams(response.token, xorKey, instanceConfig)

    // Save settings to instance config
    setSettings(instanceConfig, "token", response.token)
    setSettings(instanceConfig, "regionHash", response.regionHash)
    setSettings(instanceConfig, "xorKey", xorKey)

    logger.debug("[loginService] Updated instanceConfig:", {
      instanceConfig: {
        ...instanceConfig,
        token: instanceConfig.token,
        xorKey: instanceConfig.xorKey
      }
    })

    return response
  } catch (error) {
    logger.error(`[loginService] Login failed for Continent ${continent}:`, error)
    return null
  }
}
