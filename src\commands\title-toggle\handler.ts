import type { ChatInputCommandInteraction } from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'
import { updateTitlePanelEmbed } from '../../services/title/utils/title-panel-embed.js'

export class HandleToggleTitle extends BaseCommandHandler {
  constructor(
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap: TitleToggleMap,
    roleStatusMap: RoleStatusMap
  ) {
    super(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap
    )
  }

  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return

    const status = this.interaction.options.getString('status', true)
    this.titleToggleMap[this.instanceConfig.continent] = status === 'on'

    // Update the title panel embed first
    if (this.instanceConfig.title?.messageId) {
      await updateTitlePanelEmbed(
        this.interaction.client,
        this.instanceConfig,
        this.roleStatusMap,
        this.titleToggleMap
      )
    }

    await this.interaction.reply({
      content: `‼️ Title requests have been ${status === 'on' ? 'enabled' : 'disabled'} for Continent ${this.instanceConfig.continent} ‼️`
    })

    this.logger.info(
      `Title request toggled to '${status}' for Continent ${this.instanceConfig.continent}. Current map: ${JSON.stringify(this.titleToggleMap)}`
    )
  }
}

export default {
  data: {
    name: 'title_toggle',
    description: 'Toggle title requests on or off',
    options: [
      {
        name: 'status',
        description: 'Enable or disable title requests',
        type: 3,
        required: true,
        choices: [
          { name: 'On', value: 'on' },
          { name: 'Off', value: 'off' }
        ]
      }
    ]
  },
  CommandHandlerClass: HandleToggleTitle
}
