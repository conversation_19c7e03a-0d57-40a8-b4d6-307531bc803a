import { logger } from '../logging/logger.js'
import {
 MAX_RETRIES, TIMEOUT 
} from '../time/constants.js'

import { RetryableApiException } from './error.js'

interface RetryOptions {
  maxRetries?: number
  timeout?: number
  onRetry?: (error: Error, attempt: number) => void
  shouldRetry?: (error: Error) => boolean
}

export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const {
    maxRetries = MAX_RETRIES,
    timeout = TIMEOUT,
    onRetry = (error, attempt) => {
      logger.warn(`Retry attempt ${attempt} failed:`, error)
    },
    shouldRetry = error => error instanceof RetryableApiException
  } = options

  let lastError: Error | undefined

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await Promise.race([
        fn(),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Operation timed out')), timeout)
        )
      ])
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))

      if (attempt < maxRetries && shouldRetry(lastError)) {
        onRetry(lastError, attempt)
        const retryDelay =
          lastError instanceof RetryableApiException
            ? lastError.retryDelay
            : Math.pow(2, attempt) * 1000
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      } else {
        break
      }
    }
  }

  throw lastError || new Error(`Operation failed after ${maxRetries} retries`)
}
