/* eslint-disable @typescript-eslint/no-unused-vars */
import type {
 ButtonInteraction, TextChannel 
} from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types'

import type { InstanceConfig } from '../../../interfaces/config'
import type { ApiService } from '../../../services/api/api-service'
import {
  activateGlobalSkill,
  getSkillName
} from '../../../services/skill/service'
import { updateSkillsPanel } from '../../../services/skill/utils/skills-panel-embed'
import { logger } from '../../../utils/logging/logger'

export async function handleActivateSkill(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap
): Promise<void> {
  try {
    await interaction.deferUpdate()

    const skillCode = interaction.customId.split('-')[2]
    const skillName = getSkillName(skillCode)

    const result = await activateGlobalSkill(instanceConfig, skillCode)

    if (result.result) {
      await interaction.followUp({
        content: `Successfully activated ${skillName} for ${result.duration} minutes.`,
        ephemeral: true
      })

      if (instanceConfig.skill?.alertChannelId) {
        const alertChannel = interaction.client.channels.cache.get(
          instanceConfig.skill.alertChannelId
        ) as TextChannel | undefined
        if (alertChannel && alertChannel.isTextBased()) {
          await alertChannel.send(
            `@everyone **${skillName}** has been Activated!`
          )
        } else {
          logger.error(
            `Alert channel (${instanceConfig.skill.alertChannelId}) is not a text channel or not found.`
          )
        }
      }
    } else {
      await interaction.followUp({
        content: `Failed to activate ${skillName}. Please try again later.`,
        ephemeral: true
      })
    }

    await updateSkillsPanel(interaction.client, instanceConfig)
  } catch (error) {
    logger.error('Error activating global skill:', error)
    await interaction.followUp({
      content: 'An error occurred while activating the skill.',
      ephemeral: true
    })
  }
}
