import type {
  ChatInputCommandInteraction,
  ButtonInteraction,
  StringSelectMenuInteraction,
  ModalSubmitInteraction
} from 'discord.js'
import type { ApiService } from '../services/api/api-service.js'
import type { SocketService } from '../services/socket/service.js'
import type {
  RoleStatusMap,
  TitleToggleMap
} from '../services/title/types.js'
import type { InstanceConfig, BotConfig } from './config.js'

/**
 * Unified context object that consolidates all parameters passed to command handlers
 * This replaces the 6-parameter pattern used across all handlers
 */
export interface CommandContext {
  readonly interaction: ChatInputCommandInteraction
  readonly instanceConfig: InstanceConfig
  readonly apiInstance: ApiService
  readonly titleToggleMap?: TitleToggleMap
  readonly roleStatusMap?: RoleStatusMap
  readonly socketService?: SocketService
}

/**
 * Context for button interaction handlers
 */
export interface ButtonContext {
  readonly interaction: ButtonInteraction
  readonly instanceConfig: InstanceConfig
  readonly apiInstance: ApiService
  readonly titleToggleMap?: TitleToggleMap
  readonly roleStatusMap?: RoleStatusMap
  readonly socketService?: SocketService
}

/**
 * Context for select menu interaction handlers
 */
export interface SelectMenuContext {
  readonly interaction: StringSelectMenuInteraction
  readonly instanceConfig: InstanceConfig
  readonly apiInstance: ApiService
  readonly titleToggleMap?: TitleToggleMap
  readonly roleStatusMap?: RoleStatusMap
  readonly socketService?: SocketService
}

/**
 * Context for modal interaction handlers
 */
export interface ModalContext {
  readonly interaction: ModalSubmitInteraction
  readonly instanceConfig: InstanceConfig
  readonly apiInstance: ApiService
  readonly titleToggleMap?: TitleToggleMap
  readonly roleStatusMap?: RoleStatusMap
  readonly socketService?: SocketService
}

/**
 * Union type for all interaction contexts
 */
export type InteractionContext = CommandContext | ButtonContext | SelectMenuContext | ModalContext

/**
 * Configuration context for cleaner config access patterns
 */
export interface ConfigContext {
  readonly instance: InstanceConfig
  readonly bot: BotConfig
  readonly continent: number
}

/**
 * Service context for unified service access
 */
export interface ServiceContext {
  readonly api: ApiService
  readonly socket?: SocketService
  readonly continent: number
}

/**
 * Factory function to create CommandContext from individual parameters
 * Provides backward compatibility during migration
 */
export function createCommandContext(
  interaction: ChatInputCommandInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap?: TitleToggleMap,
  roleStatusMap?: RoleStatusMap,
  socketService?: SocketService
): CommandContext {
  return {
    interaction,
    instanceConfig,
    apiInstance,
    titleToggleMap,
    roleStatusMap,
    socketService
  }
}

/**
 * Factory function to create ButtonContext from individual parameters
 */
export function createButtonContext(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap?: TitleToggleMap,
  roleStatusMap?: RoleStatusMap,
  socketService?: SocketService
): ButtonContext {
  return {
    interaction,
    instanceConfig,
    apiInstance,
    titleToggleMap,
    roleStatusMap,
    socketService
  }
}

/**
 * Factory function to create SelectMenuContext from individual parameters
 */
export function createSelectMenuContext(
  interaction: StringSelectMenuInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap?: TitleToggleMap,
  roleStatusMap?: RoleStatusMap,
  socketService?: SocketService
): SelectMenuContext {
  return {
    interaction,
    instanceConfig,
    apiInstance,
    titleToggleMap,
    roleStatusMap,
    socketService
  }
}

/**
 * Factory function to create ModalContext from individual parameters
 */
export function createModalContext(
  interaction: ModalSubmitInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap?: TitleToggleMap,
  roleStatusMap?: RoleStatusMap,
  socketService?: SocketService
): ModalContext {
  return {
    interaction,
    instanceConfig,
    apiInstance,
    titleToggleMap,
    roleStatusMap,
    socketService
  }
}
