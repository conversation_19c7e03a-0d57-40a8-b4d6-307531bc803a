import {
  ActionRowBuilder,
  StringSelectMenuBuilder,
  type ButtonInteraction
} from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import type { InstanceConfig } from '../../../interfaces/config.js'
import type { ApiService } from '../../../services/api/api-service.js'
import { getPlayerProfiles } from '../../../services/storage/user-data-storage.js'

type ButtonHandlerFunction = (
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap
) => Promise<void>

async function handleSelectRole(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap,
  roleName: 'Alchemist' | 'Architect'
): Promise<void> {
  if (!titleToggleMap[instanceConfig.continent]) {
    await interaction.reply({
      content: 'Title requests are currently disabled.',
      ephemeral: true
    })
    return
  }

  const currentRoleStatus = roleStatusMap[instanceConfig.continent]
  if (currentRoleStatus && currentRoleStatus.hasRole) {
    await interaction.reply({
      content:
        "A role is currently assigned. Please wait for it to expire or use the 'Expire Role' button.",
      ephemeral: true
    })
    return
  }

  const playerProfiles = await getPlayerProfiles(
    instanceConfig,
    interaction.user.id
  )

  if (playerProfiles.length === 0) {
    await interaction.reply({
      content:
        'You have no linked kingdoms. Please add a kingdom first using the `/add_kingdom` command.',
      ephemeral: true
    })
    return
  }

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId(`select-kingdom-${roleName}`)
    .setPlaceholder('Select a kingdom')
    .addOptions(
      playerProfiles.map(profile => ({
        label: profile.name,
        value: profile.id,
        description: `Kingdom ID: ${profile.id}`
      }))
    )

  const row = new ActionRowBuilder<StringSelectMenuBuilder>().addComponents(
    selectMenu
  )

  await interaction.reply({
    content: `Please select a kingdom for the ${roleName} role:`,
    components: [row],
    ephemeral: true
  })
}

export const handleSelectAlchemist: ButtonHandlerFunction = async (
  interaction,
  instanceConfig,
  apiInstance,
  titleToggleMap,
  roleStatusMap
) => {
  await handleSelectRole(
    interaction,
    instanceConfig,
    apiInstance,
    titleToggleMap,
    roleStatusMap,
    'Alchemist'
  )
}

export const handleSelectArchitect: ButtonHandlerFunction = async (
  interaction,
  instanceConfig,
  apiInstance,
  titleToggleMap,
  roleStatusMap
) => {
  await handleSelectRole(
    interaction,
    instanceConfig,
    apiInstance,
    titleToggleMap,
    roleStatusMap,
    'Architect'
  )
}
