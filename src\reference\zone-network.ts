import { zoneUpdate } from "../../../server/commands/zone-update"
import { b64xorEnc } from "../../../utils/decode"
import { wait } from "../../../utils/timer"
import type { MapSearchParams } from "./types"

const zoneMappers: Record<string, Set<number>> = {}

export function getZoneMapper(clientId: string): Set<number> {
  if (!zoneMappers[clientId]) {
    zoneMappers[clientId] = new Set()
  }

  return zoneMappers[clientId]
}

export class ZoneNetwork {
  private socketWrap: MapSearchParams["socketWrap"]
  private token: string
  private xorKey: string
  private worldId: number
  private clientId: string
  private enteredZones: Set<number>

  constructor(
    socketWrap: MapSearchParams["socketWrap"],
    token: string,
    xorKey: string,
    worldId: number,
    clientId: string,
  ) {
    this.socketWrap = socketWrap
    this.token = token
    this.xorKey = xorKey
    this.worldId = worldId
    this.clientId = clientId
    this.enteredZones = getZoneMapper(clientId)
  }

  async exitAllZones(): Promise<void> {
    await this.exitZones(Array.from(this.enteredZones))
  }

  async exitZones(exitZones: number[]): Promise<void> {
    const exitPayload = {
      world: this.worldId,
      zones: JSON.stringify(exitZones),
    }

    const message = JSON.stringify(["/zone/leave/list/v2", exitPayload])
    await zoneUpdate(message, this.clientId, this.xorKey)
    await this.socketWrap.send("wss://socf-lok-live.leagueofkingdoms.com", message)
    await wait(100)
  }

  async enterZones(zones: number[]): Promise<void> {
    const zonesToLeave = Array.from(this.enteredZones).filter((z) => !zones.includes(z))

    await this.exitZones(zonesToLeave)

    const enterPayload = b64xorEnc(this.xorKey, {
      world: this.worldId,
      zones: JSON.stringify(zones),
      compType: 3,
    })

    const message = JSON.stringify(["/zone/enter/list/v4", enterPayload])
    await zoneUpdate(message, this.clientId, this.xorKey)
    await this.socketWrap.send("wss://socf-lok-live.leagueofkingdoms.com", message)
  }
}