import WebSocket from "ws"

import { logger } from "../../../utils/logging/logger.js"

import {
 ConnectionState, type SocketOptions, type SocketType
} from "../types.js"

export interface ConnectionStats {
  messageCount: { sent: number; received: number }
  lastActivity: number
  uptime: number
  state: ConnectionState
}

export class SocketConnection {
  private socket: WebSocket | null = null
  private pingTimer: NodeJS.Timeout | null = null
  private reconnectTimer: NodeJS.Timeout | null = null
  private reconnectAttempts = 0
  private state = ConnectionState.DISCONNECTED
  private messageCount = { sent: 0, received: 0 }
  private lastActivity = Date.now()
  private connectionStartTime = 0
  private pingInterval = 25000
  private connectResolve: (() => void) | null = null

  constructor(
    private readonly type: SocketType,
    private readonly url: string,
    private readonly options: Required<SocketOptions>,
    private readonly onMessage: (path: string, payload: unknown) => void,
    private readonly onStateChange: (state: ConnectionState) => void,
  ) {}

  public async connect(): Promise<void> {
    if (
      this.state === ConnectionState.CONNECTING ||
      this.state === ConnectionState.CONNECTED ||
      this.state === ConnectionState.INITIALIZED
    ) {
      logger.debug(`[SocketConnection:${this.type}] Connect called but state is ${this.state}, not DISCONNECTED`)
      return
    }

    this.updateState(ConnectionState.CONNECTING)
    this.reconnectAttempts = 0

    logger.info(`[SocketConnection:${this.type}] Connecting to ${this.url}`)
    logger.debug(`[SocketConnection:${this.type}] Connection options: ${JSON.stringify({
      perMessageDeflate: false,
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
      },
      timeout: this.options.connectionTimeout,
      maxReconnects: this.options.maxReconnectAttempts,
      reconnectDelay: this.options.reconnectDelay
    })}`)

    return new Promise<void>((resolve, reject) => {
      this.connectResolve = resolve

      try {
        logger.debug(`[SocketConnection:${this.type}] Creating WebSocket instance`)

        this.socket = new WebSocket(this.url, {
          perMessageDeflate: false,
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
          },
        })

        const timeout = setTimeout(() => {
          if (this.socket) {
            this.socket.terminate()
          }
          this.updateState(ConnectionState.DISCONNECTED)
          this.connectResolve = null
          reject(new Error(`Connection timeout for ${this.type}`))
        }, this.options.connectionTimeout)

        this.socket.on("open", () => {
          clearTimeout(timeout)
          this.connectionStartTime = Date.now()
          this.lastActivity = Date.now()
          this.updateState(ConnectionState.CONNECTED)
          logger.info(`[SocketConnection:${this.type}] WebSocket connection opened`)

          // Log socket details
          const socketDetails = {
            url: this.url,
            readyState: this.socket ? this.socket.readyState : 'null',
            protocol: this.socket ? this.socket.protocol : 'null',
          }
          logger.debug(`[SocketConnection:${this.type}] Socket details: ${JSON.stringify(socketDetails)}`)
        })

        this.socket.on("message", (data) => {
          // Detailed logging is in handleSocketMessage
          this.handleSocketMessage(data.toString())
        })

        this.socket.on("close", (code, reason) => {
          logger.warn(`[SocketConnection:${this.type}] WebSocket closed: code=${code}, reason=${reason}`)
          this.updateState(ConnectionState.DISCONNECTED)
          this.clearPingTimer()

          // Log connection stats at close
          const stats = this.getStats()
          logger.info(`[SocketConnection:${this.type}] Connection stats at close: ${JSON.stringify(stats)}`)

          if (this.socket) {
            this.socket = null
            this.handleReconnection()
          }
        })

        this.socket.on("error", (error) => {
          logger.error(`[SocketConnection:${this.type}] WebSocket error: ${error}`)
          clearTimeout(timeout)
          this.updateState(ConnectionState.DISCONNECTED)
          this.connectResolve = null
          reject(error)
        })
      } catch (error) {
        this.updateState(ConnectionState.DISCONNECTED)
        this.connectResolve = null
        reject(error)
      }
    })
  }

  private handleSocketMessage(message: string) {
    this.messageCount.received++
    this.lastActivity = Date.now()

    // Log all raw messages for debugging
    logger.debug(`[SocketConnection:${this.type}] RAW RECEIVED: ${message}`)

    if (message === "2") {
      // Socket.io ping
      logger.debug(`[SocketConnection:${this.type}] Received PING`)
      if (this.socket?.readyState === WebSocket.OPEN) {
        this.socket.send("3")
        this.messageCount.sent++
        logger.debug(`[SocketConnection:${this.type}] Sent PONG`)
      }
      return
    }

    if (message === "3") {
      // Socket.io pong
      logger.debug(`[SocketConnection:${this.type}] Received PONG`)
      this.schedulePing()
      return
    }

    if (message.startsWith("0")) {
      // Socket.io handshake
      const handshakeData = JSON.parse(message.substring(1))
      logger.info(`[SocketConnection:${this.type}] Handshake received: ${JSON.stringify(handshakeData)}`)

      // Log detailed handshake information for debugging
      logger.info(`[SocketConnection:${this.type}] Handshake details:
        - sid: ${handshakeData.sid}
        - upgrades: ${JSON.stringify(handshakeData.upgrades)}
        - pingInterval: ${handshakeData.pingInterval}
        - pingTimeout: ${handshakeData.pingTimeout}
        - maxPayload: ${handshakeData.maxPayload}
      `)

      if (handshakeData.pingInterval) {
        this.pingInterval = handshakeData.pingInterval
        logger.debug(`[SocketConnection:${this.type}] Set ping interval to ${this.pingInterval}ms`)
      }
      return
    }

    if (message === "40") {
      // Socket.io connection established
      logger.info(`[SocketConnection:${this.type}] Connection established (40)`)
      this.updateState(ConnectionState.INITIALIZED)
      this.schedulePing()

      // Send a special message to identify this client
      if (this.socket?.readyState === WebSocket.OPEN) {
        // This is a diagnostic message that might help identify the client to the server
        logger.info(`[SocketConnection:${this.type}] Sending client identification`)
        this.socket.send(`42["client-info",{"type":"bot","version":"1.0.0","connectionTime":${Date.now()}}]`)
        this.messageCount.sent++
      }

      if (this.connectResolve) {
        this.connectResolve()
        this.connectResolve = null
      }

      return
    }

    if (message.startsWith("42")) {
      // Socket.io event
      try {
        const jsonData = JSON.parse(message.substring(2))
        if (Array.isArray(jsonData) && jsonData.length >= 2) {
          const [path, payload] = jsonData
          logger.debug(`[SocketConnection:${this.type}] Event received: ${path}`)
          this.onMessage(path, payload)
        } else {
          logger.warn(`[SocketConnection:${this.type}] Invalid event format: ${message}`)
        }
      } catch (error) {
        logger.error(`[SocketConnection:${this.type}] Failed to parse message: ${error}`)
      }
    } else {
      // Any other message type
      logger.debug(`[SocketConnection:${this.type}] Unhandled message type: ${message}`)
    }
  }

  private schedulePing() {
    this.clearPingTimer()

    const pingTime = this.pingInterval + Math.floor(Math.random() * 200) - 100

    this.pingTimer = setTimeout(() => {
      if (this.socket?.readyState === WebSocket.OPEN) {
        this.socket.send("2")
        this.messageCount.sent++
        this.lastActivity = Date.now()
      }
    }, pingTime)
  }

  private clearPingTimer() {
    if (this.pingTimer) {
      clearTimeout(this.pingTimer)
      this.pingTimer = null
    }
  }

  private handleReconnection() {
    if (this.state === ConnectionState.CLOSING || this.state === ConnectionState.CLOSED) return

    this.updateState(ConnectionState.RECONNECTING)

    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      this.updateState(ConnectionState.DISCONNECTED)
      return
    }

    this.reconnectAttempts++
    const attempt = this.reconnectAttempts

    const delay = Math.min(
      this.options.reconnectDelay * Math.pow(1.5, attempt - 1) * (0.9 + Math.random() * 0.2),
      30000,
    )

    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null

      if (this.state !== ConnectionState.RECONNECTING) return

      logger.info(`[SocketConnection] Attempting to reconnect ${this.type} (attempt ${attempt})`)

      this.connect().catch((error) => {
        logger.error(`[SocketConnection] Failed to reconnect ${this.type}: ${error}`)
      })
    }, delay)
  }

  public close() {
    if (this.state === ConnectionState.CLOSING || this.state === ConnectionState.CLOSED) return

    this.updateState(ConnectionState.CLOSING)
    this.clearPingTimer()

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.socket) {
      if (this.socket.readyState === WebSocket.OPEN) {
        this.socket.send("41")
        this.socket.close()
      } else if (this.socket.readyState === WebSocket.CONNECTING) {
        this.socket.terminate()
      }
      this.socket = null
    }

    this.updateState(ConnectionState.CLOSED)
  }

  public sendRaw(message: string) {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      logger.warn(`[SocketConnection:${this.type}] Cannot send message: socket not open (state: ${this.socket?.readyState})`)
      throw new Error(`Cannot send message: socket not open`)
    }

    // Log outgoing messages
    logger.debug(`[SocketConnection:${this.type}] RAW SENDING: ${message}`)

    this.socket.send(message)
    this.messageCount.sent++
    this.lastActivity = Date.now()
  }

  public isReady(): boolean {
    return (
      (this.state === ConnectionState.CONNECTED || this.state === ConnectionState.INITIALIZED) &&
      !!this.socket &&
      this.socket.readyState === WebSocket.OPEN
    )
  }

  public getState(): ConnectionState {
    return this.state
  }

  private updateState(newState: ConnectionState) {
    if (this.state === newState) return

    this.state = newState
    this.onStateChange(newState)
  }

  public getStats(): ConnectionStats {
    return {
      messageCount: { ...this.messageCount },
      lastActivity: this.lastActivity,
      uptime: this.connectionStartTime ? Date.now() - this.connectionStartTime : 0,
      state: this.state,
    }
  }
}