import type { InstanceConfig } from '../interfaces/config.js'
import type { ApiService } from '../services/api/api-service.js'
import type { SocketService } from '../services/socket/service.js'
import { logger } from '../utils/logging/logger.js'

/**
 * Service type identifiers for type-safe service registration and retrieval
 */
export enum ServiceType {
  API = 'api',
  SOCKET = 'socket',
  MONITOR_ROTATE = 'monitor_rotate',
  MONITOR_STAKER = 'monitor_staker',
  MONITOR_MANAGER = 'monitor_manager',
  EJECT = 'eject',
  STAKER = 'staker'
}

/**
 * Service metadata for tracking service lifecycle
 */
interface ServiceMetadata {
  continent: number
  initialized: Date
  instanceConfig: InstanceConfig
}

/**
 * Unified service registry that standardizes service instance management
 * across all domains. Replaces the inconsistent singleton patterns used
 * throughout the codebase.
 */
export class ServiceRegistry {
  private static instance: ServiceRegistry
  private services = new Map<ServiceType, Map<number, any>>()
  private metadata = new Map<string, ServiceMetadata>()

  private constructor() {}

  /**
   * Get the singleton instance of the service registry
   */
  static getInstance(): ServiceRegistry {
    if (!ServiceRegistry.instance) {
      ServiceRegistry.instance = new ServiceRegistry()
    }
    return ServiceRegistry.instance
  }

  /**
   * Register a service instance for a specific continent
   * @param serviceType The type of service being registered
   * @param continent The continent number
   * @param service The service instance
   * @param instanceConfig The instance configuration
   */
  register<T>(
    serviceType: ServiceType,
    continent: number,
    service: T,
    instanceConfig: InstanceConfig
  ): void {
    if (!this.services.has(serviceType)) {
      this.services.set(serviceType, new Map())
    }

    const serviceMap = this.services.get(serviceType)!
    serviceMap.set(continent, service)

    // Store metadata for tracking
    const metadataKey = `${serviceType}_${continent}`
    this.metadata.set(metadataKey, {
      continent,
      initialized: new Date(),
      instanceConfig
    })

    logger.debug(`[ServiceRegistry] Registered ${serviceType} service for Continent ${continent}`)
  }

  /**
   * Retrieve a service instance for a specific continent
   * @param serviceType The type of service to retrieve
   * @param continent The continent number
   * @returns The service instance
   * @throws Error if service is not registered
   */
  get<T>(serviceType: ServiceType, continent: number): T {
    const serviceMap = this.services.get(serviceType)
    if (!serviceMap?.has(continent)) {
      throw new Error(`${serviceType} service not initialized for Continent ${continent}`)
    }
    return serviceMap.get(continent) as T
  }

  /**
   * Check if a service is registered for a specific continent
   * @param serviceType The type of service to check
   * @param continent The continent number
   * @returns True if service is registered
   */
  has(serviceType: ServiceType, continent: number): boolean {
    const serviceMap = this.services.get(serviceType)
    return serviceMap?.has(continent) ?? false
  }

  /**
   * Unregister a service instance for a specific continent
   * @param serviceType The type of service to unregister
   * @param continent The continent number
   */
  unregister(serviceType: ServiceType, continent: number): void {
    const serviceMap = this.services.get(serviceType)
    if (serviceMap?.has(continent)) {
      serviceMap.delete(continent)
      
      const metadataKey = `${serviceType}_${continent}`
      this.metadata.delete(metadataKey)
      
      logger.debug(`[ServiceRegistry] Unregistered ${serviceType} service for Continent ${continent}`)
    }
  }

  /**
   * Get all registered continents for a specific service type
   * @param serviceType The type of service
   * @returns Array of continent numbers
   */
  getContinents(serviceType: ServiceType): number[] {
    const serviceMap = this.services.get(serviceType)
    return serviceMap ? Array.from(serviceMap.keys()) : []
  }

  /**
   * Get metadata for a specific service instance
   * @param serviceType The type of service
   * @param continent The continent number
   * @returns Service metadata or undefined if not found
   */
  getMetadata(serviceType: ServiceType, continent: number): ServiceMetadata | undefined {
    const metadataKey = `${serviceType}_${continent}`
    return this.metadata.get(metadataKey)
  }

  /**
   * Get all registered services for debugging/monitoring
   * @returns Map of service types to continent arrays
   */
  getAllServices(): Map<ServiceType, number[]> {
    const result = new Map<ServiceType, number[]>()
    for (const [serviceType, serviceMap] of this.services) {
      result.set(serviceType, Array.from(serviceMap.keys()))
    }
    return result
  }

  /**
   * Clear all services (primarily for testing)
   */
  clear(): void {
    this.services.clear()
    this.metadata.clear()
    logger.debug('[ServiceRegistry] Cleared all services')
  }

  /**
   * Get service statistics for monitoring
   */
  getStats(): {
    totalServices: number
    serviceTypes: number
    continents: Set<number>
  } {
    let totalServices = 0
    const continents = new Set<number>()

    for (const serviceMap of this.services.values()) {
      totalServices += serviceMap.size
      for (const continent of serviceMap.keys()) {
        continents.add(continent)
      }
    }

    return {
      totalServices,
      serviceTypes: this.services.size,
      continents
    }
  }
}

/**
 * Convenience functions for common service operations
 */

/**
 * Get the API service for a specific continent
 */
export function getApiService(continent: number): ApiService {
  return ServiceRegistry.getInstance().get<ApiService>(ServiceType.API, continent)
}

/**
 * Get the Socket service for a specific continent
 */
export function getSocketService(continent: number): SocketService {
  return ServiceRegistry.getInstance().get<SocketService>(ServiceType.SOCKET, continent)
}

/**
 * Check if API service is available for a continent
 */
export function hasApiService(continent: number): boolean {
  return ServiceRegistry.getInstance().has(ServiceType.API, continent)
}

/**
 * Check if Socket service is available for a continent
 */
export function hasSocketService(continent: number): boolean {
  return ServiceRegistry.getInstance().has(ServiceType.SOCKET, continent)
}

/**
 * Register API service for a continent
 */
export function registerApiService(
  continent: number,
  service: ApiService,
  instanceConfig: InstanceConfig
): void {
  ServiceRegistry.getInstance().register(ServiceType.API, continent, service, instanceConfig)
}

/**
 * Register Socket service for a continent
 */
export function registerSocketService(
  continent: number,
  service: SocketService,
  instanceConfig: InstanceConfig
): void {
  ServiceRegistry.getInstance().register(ServiceType.SOCKET, continent, service, instanceConfig)
}
