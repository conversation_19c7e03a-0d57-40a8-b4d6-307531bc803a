import type {
  ChatInputCommandInteraction,
  ButtonInteraction,
  StringSelectMenuInteraction,
  ModalSubmitInteraction,
  InteractionReplyOptions,
  InteractionEditReplyOptions
} from 'discord.js'
import { BotError } from '../common/error.js'
import { logger } from '../logging/logger.js'

/**
 * Union type for all interaction types that can have errors
 */
type SupportedInteraction = 
  | ChatInputCommandInteraction 
  | ButtonInteraction 
  | StringSelectMenuInteraction 
  | ModalSubmitInteraction

/**
 * Error handling options for customizing error responses
 */
interface ErrorHandlingOptions {
  /** Custom error message to display to user */
  customMessage?: string
  /** Whether the error response should be ephemeral (default: true) */
  ephemeral?: boolean
  /** Whether to log the full error details (default: true) */
  logError?: boolean
  /** Additional context for logging */
  context?: Record<string, any>
}

/**
 * Centralized interaction error handler that eliminates duplicated error handling
 * logic across CommandInteractions, ButtonInteractions, InteractionHandler, etc.
 * 
 * This replaces the repeated error handling patterns found in:
 * - BaseCommandHandler.handleError()
 * - CommandUtils.handleCommandError()
 * - InteractionHandler error handling
 * - ButtonInteractions error handling
 */
export class InteractionErrorHandler {
  /**
   * Handle errors for any type of Discord interaction
   * @param interaction The Discord interaction that encountered an error
   * @param error The error that occurred
   * @param options Error handling options
   */
  static async handleError(
    interaction: SupportedInteraction,
    error: unknown,
    options: ErrorHandlingOptions = {}
  ): Promise<void> {
    const {
      customMessage,
      ephemeral = true,
      logError = true,
      context = {}
    } = options

    // Log the error with context
    if (logError) {
      const errorContext = {
        interactionType: interaction.type,
        interactionId: interaction.id,
        userId: interaction.user.id,
        guildId: interaction.guildId,
        channelId: interaction.channelId,
        ...context
      }

      if (error instanceof BotError) {
        logger.error(`[InteractionError] BotError in ${interaction.constructor.name}:`, {
          message: error.message,
          code: error.code,
          status: error.status,
          retryable: error.retryable,
          ...errorContext
        })
      } else if (error instanceof Error) {
        logger.error(`[InteractionError] Error in ${interaction.constructor.name}:`, {
          message: error.message,
          stack: error.stack,
          ...errorContext
        })
      } else {
        logger.error(`[InteractionError] Unknown error in ${interaction.constructor.name}:`, {
          error: String(error),
          ...errorContext
        })
      }
    }

    // Determine the appropriate error message
    const errorMessage = customMessage || this.getDefaultErrorMessage(error)

    // Send error response to user
    await this.sendErrorResponse(interaction, errorMessage, ephemeral)
  }

  /**
   * Handle command-specific errors with additional command context
   * @param interaction The command interaction
   * @param error The error that occurred
   * @param commandName The name of the command that failed
   * @param options Error handling options
   */
  static async handleCommandError(
    interaction: ChatInputCommandInteraction,
    error: unknown,
    commandName: string,
    options: ErrorHandlingOptions = {}
  ): Promise<void> {
    const commandContext = {
      commandName,
      options: interaction.options.data.map(opt => ({
        name: opt.name,
        type: opt.type,
        value: opt.value
      }))
    }

    await this.handleError(interaction, error, {
      ...options,
      context: { ...options.context, ...commandContext }
    })
  }

  /**
   * Handle button interaction errors with button context
   * @param interaction The button interaction
   * @param error The error that occurred
   * @param buttonId The custom ID of the button that failed
   * @param options Error handling options
   */
  static async handleButtonError(
    interaction: ButtonInteraction,
    error: unknown,
    buttonId: string,
    options: ErrorHandlingOptions = {}
  ): Promise<void> {
    const buttonContext = {
      buttonId,
      componentType: interaction.componentType
    }

    await this.handleError(interaction, error, {
      ...options,
      context: { ...options.context, ...buttonContext }
    })
  }

  /**
   * Get default error message based on error type
   * @param error The error that occurred
   * @returns Appropriate user-facing error message
   */
  private static getDefaultErrorMessage(error: unknown): string {
    if (error instanceof BotError) {
      switch (error.code) {
        case 'LOGIN_FAILED':
        case 'no_auth':
          return 'Authentication failed. Please try again later.'
        case 'not_king':
          return 'You do not have the required permissions for this action.'
        case 'API_ERROR':
          return 'There was a problem communicating with the game server. Please try again.'
        case 'ROLE_ASSIGNMENT_ERROR':
          return 'Failed to assign role. Please check permissions and try again.'
        default:
          return 'An error occurred while processing your request.'
      }
    }

    return 'An unexpected error occurred. Please try again later.'
  }

  /**
   * Send error response to the user via the interaction
   * @param interaction The Discord interaction
   * @param message The error message to send
   * @param ephemeral Whether the response should be ephemeral
   */
  private static async sendErrorResponse(
    interaction: SupportedInteraction,
    message: string,
    ephemeral: boolean
  ): Promise<void> {
    try {
      const responseOptions: InteractionReplyOptions = {
        content: message,
        ephemeral
      }

      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply(responseOptions)
      } else if (!interaction.replied) {
        const editOptions: InteractionEditReplyOptions = {
          content: message
        }
        await interaction.editReply(editOptions)
      } else {
        // Interaction already replied, try to follow up
        await interaction.followUp({
          content: message,
          ephemeral: true
        })
      }
    } catch (replyError) {
      logger.error('[InteractionErrorHandler] Failed to send error response:', {
        originalMessage: message,
        replyError: replyError instanceof Error ? replyError.message : String(replyError),
        interactionId: interaction.id,
        replied: interaction.replied,
        deferred: interaction.deferred
      })
    }
  }

  /**
   * Create a standardized error context object for logging
   * @param interaction The Discord interaction
   * @param additionalContext Additional context to include
   * @returns Error context object
   */
  static createErrorContext(
    interaction: SupportedInteraction,
    additionalContext: Record<string, any> = {}
  ): Record<string, any> {
    return {
      interactionType: interaction.type,
      interactionId: interaction.id,
      userId: interaction.user.id,
      username: interaction.user.username,
      guildId: interaction.guildId,
      channelId: interaction.channelId,
      timestamp: new Date().toISOString(),
      ...additionalContext
    }
  }
}

/**
 * Convenience function for handling command errors
 * Maintains backward compatibility with existing error handling patterns
 */
export async function handleCommandError(
  interaction: ChatInputCommandInteraction,
  error: unknown,
  customMessage?: string
): Promise<void> {
  await InteractionErrorHandler.handleCommandError(
    interaction,
    error,
    interaction.commandName,
    { customMessage }
  )
}

/**
 * Convenience function for handling button errors
 */
export async function handleButtonError(
  interaction: ButtonInteraction,
  error: unknown,
  customMessage?: string
): Promise<void> {
  await InteractionErrorHandler.handleButtonError(
    interaction,
    error,
    interaction.customId,
    { customMessage }
  )
}
