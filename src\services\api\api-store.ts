import type { ApiService } from './api-service.js'
import type { InstanceConfig } from '../../interfaces/config.js'
import { logger } from '../../utils/logging/logger.js'

class ApiStore {
  private static instance: ApiStore
  private apiInstances: Map<number, ApiService> = new Map()
  private instanceConfigs: Map<number, InstanceConfig> = new Map()

  private constructor() {}

  static getInstance(): ApiStore {
    if (!ApiStore.instance) {
      ApiStore.instance = new ApiStore()
    }
    return ApiStore.instance
  }

  setInstance(continent: number, service: ApiService, config: InstanceConfig): void {
    this.apiInstances.set(continent, service)
    this.instanceConfigs.set(continent, config)
    logger.debug('[ApiStore] Stored instance:', {
      continent,
      hasInstance: this.hasInstance(continent),
      allInstances: Array.from(this.apiInstances.keys())
    })
  }

  getInstance(continent: number): ApiService | undefined {
    return this.apiInstances.get(continent)
  }

  hasInstance(continent: number): boolean {
    return this.apiInstances.has(continent)
  }

  getConfig(continent: number): InstanceConfig | undefined {
    return this.instanceConfigs.get(continent)
  }
}

export const apiStore = ApiStore.getInstance()