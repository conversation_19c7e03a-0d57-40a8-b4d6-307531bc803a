import { <PERSON><PERSON>ommandHandler } from '../../handlers/commands/command-handler.js'
import { fetchPlayerProfile } from '../../services/alliance/alliance-service.js'
import {
  addOrUpdatePlayerProfile,
  getPlayerProfiles
} from '../../services/storage/user-data-storage.js'
import { logger } from '../../utils/logging/logger.js'

export class HandleAddKingdom extends BaseCommandHandler {
  async execute(): Promise<void> {
    const kingdomId = this.interaction.options.getString('kingdom_id', true)

    await this.ensureToken()

    try {
      const existingProfiles = await getPlayerProfiles(
        this.instanceConfig,
        this.interaction.user.id
      )
      const existingProfile = existingProfiles.find(
        profile => profile.id === kingdomId
      )

      if (existingProfile) {
        await this.interaction.reply({
          content: `Kingdom "${existingProfile.name}" (ID: ${kingdomId}) is already linked to your account.`,
          ephemeral: true
        })
        logger.info(
          `User ${this.interaction.user.id} attempted to add an existing kingdom: ${existingProfile.name} (${kingdomId}) for continent ${this.instanceConfig.continent}`
        )
        return
      }

      const newProfile = await fetchPlayerProfile(
        this.instanceConfig,
        kingdomId
      )

      if (newProfile.continent !== this.instanceConfig.continent) {
        await this.interaction.reply({
          content: `Kingdom "${newProfile.name}" belongs to Continent ${newProfile.continent}, not the current Continent ${this.instanceConfig.continent}.`,
          ephemeral: true
        })
        return
      }

      await addOrUpdatePlayerProfile(
        this.instanceConfig,
        this.interaction.user.id,
        newProfile
      )

      await this.interaction.reply({
        content: `Kingdom "${newProfile.name}" (ID: ${kingdomId}) has been added successfully.`,
        ephemeral: true
      })
      logger.info(
        `User ${this.interaction.user.id} added kingdom: ${newProfile.name} (${kingdomId}) for continent ${this.instanceConfig.continent}`
      )
    } catch (error) {
      logger.error('Error in HandleAddKingdom:', error)
      await this.interaction.reply({
        content:
          'An error occurred while adding the kingdom. Please try again.',
        ephemeral: true
      })
    }
  }
}

export default {
  data: {
    name: 'add_kingdom',
    description: 'Add a kingdom to your profile',
    options: [
      {
        name: 'kingdom_id',
        type: 3, // STRING type
        description: 'The ID of the kingdom to add',
        required: true
      }
    ]
  },
  CommandHandlerClass: HandleAddKingdom
}
