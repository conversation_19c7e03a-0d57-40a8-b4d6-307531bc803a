import type { InstanceConfig } from '../../interfaces/config.js'
import type { PlayerProfile } from '../../interfaces/player.js'
import { BotError } from '../../utils/common/error.js'
import { logger } from '../../utils/logging/logger.js'
import { getApiInstance } from '../api/api-instance.js'

import type {
  CvcEventResponse,
  CvcRankingResponse,
  CvcDashboardResponse
} from './types.js'

export async function getCvcWorldId(
  instanceConfig: InstanceConfig
): Promise<{ cvcWorldId: number }> {
  const api = getApiInstance(instanceConfig.continent)
  const response = await api.post<CvcDashboardResponse>(
    '/event/cvc/dashboard',
    {}
  )

  if (
    !response ||
    !response.dashboard ||
    !response.dashboard.cvc ||
    typeof response.dashboard.cvc.cvcWorldId !== 'number'
  ) {
    throw new BotError(
      'Invalid response structure from CVC dashboard API',
      'CVC_DASHBOARD_ERROR'
    )
  }

  return {
    cvcWorldId: response.dashboard.cvc.cvcWorldId
  }
}

export async function getIds(instanceConfig: InstanceConfig): Promise<{
  individualId: string | null
  continentId: string | null
  isCvcActive: boolean
}> {
  const api = getApiInstance(instanceConfig.continent)

  const response = await api.post<CvcEventResponse>('/event/list/cvc', {})

  if (!response || !response.events || response.events.length === 0) {
    logger.info('No CVC events found. CVC might not be active.')
    return {
      individualId: null,
      continentId: null,
      isCvcActive: false
    }
  }

  const individualEvent = response.events.find(
    event => event.mastCode === 601080
  ) // Individual Rank
  const continentEvent = response.events.find(
    event => event.mastCode === 601082
  ) // Continent Rank

  if (!individualEvent || !continentEvent) {
    logger.warn('Required events for Individual and Continent ranks not found.')
    return {
      individualId: null,
      continentId: null,
      isCvcActive: false
    }
  }

  return {
    individualId: individualEvent._id,
    continentId: continentEvent._id,
    isCvcActive: true
  }
}

export async function fetchCvcRanks(
  instanceConfig: InstanceConfig,
  individualId: string,
  continentId: string
): Promise<PlayerProfile[]> {
  const api = getApiInstance(instanceConfig.continent)

  const [individualResponse, continentResponse] = await Promise.all([
    api.post<CvcRankingResponse>('/event/cvc/ranking', {
      eventId: individualId,
      cvcPeriod: 4
    }),
    api.post<CvcRankingResponse>('/event/cvc/ranking/continent', {
      eventId: continentId,
      worldId: instanceConfig.continent
    })
  ])

  if (!individualResponse.ranking || !continentResponse.ranking) {
    throw new BotError(
      'Invalid response structure from CVC ranking API',
      'CVC_RANK_ERROR'
    )
  }

  const kingdomMap = new Map<string, PlayerProfile>()

  for (const entry of continentResponse.ranking) {
    if (
      entry.kingdom &&
      entry.kingdom.worldId === Number(instanceConfig.continent)
    ) {
      kingdomMap.set(entry.kingdom._id, {
        id: entry.kingdom._id,
        name: entry.kingdom.name,
        continent: entry.kingdom.worldId,
        power: entry.kingdom.power,
        continentRank: entry.rank,
        individualPoints: entry.point
      })
    }
  }

  for (const entry of individualResponse.ranking) {
    if (
      entry.kingdom &&
      entry.kingdom.worldId === Number(instanceConfig.continent)
    ) {
      const existingData = kingdomMap.get(entry.kingdom._id)
      if (existingData) {
        existingData.individualRank = entry.rank
      } else {
        logger.warn(
          `Found individual rank for kingdom ${entry.kingdom._id} without continent rank. Skipping.`
        )
      }
    }
  }

  const rankData = Array.from(kingdomMap.values())

  if (rankData.length === 0) {
    throw new BotError('No rank data found', 'CVC_RANK_ERROR')
  }

  return rankData
}
