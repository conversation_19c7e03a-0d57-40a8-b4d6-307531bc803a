import { ButtonStyle } from 'discord.js'

import { BaseCommandHandler } from '../../handlers/commands/command-handler'
import { createManagerPanel } from '../../services/monitor/manager/utils/manager-panel-embed'
import { setSettings } from '../../utils/config/config-utils'
import { logger } from '../../utils/logging/logger'

export class HandleAllianceManager extends BaseCommandHandler {
  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return

    await this.ensureToken()

    await this.interaction.deferReply({ ephemeral: true })

    try {
      const message = await this.interaction.channel?.send({
        ...createManagerPanel(this.instanceConfig),
        components: [this.createManagerButtons()]
      })

      if (message) {
        this.instanceConfig.manage.messageId = message.id
        this.instanceConfig.manage.channelId = message.channel.id

        await setSettings(this.instanceConfig, 'manage.messageId', message.id)
        await setSettings(
          this.instanceConfig,
          'manage.channelId',
          message.channel.id
        )

        await this.interaction.editReply({
          content: 'Alliance manager panel has been created.'
        })
      }
    } catch (error) {
      logger.error('Error creating alliance manager panel:', error)
      await this.interaction.editReply({
        content: 'Failed to create alliance manager panel.'
      })
    }
  }

  private createManagerButtons() {
    return this.messageUtils.createActionRow(
      this.messageUtils.createButton(
        'enable-manager',
        'Start Manager',
        ButtonStyle.Success
      ),
      this.messageUtils.createButton(
        'disable-manager',
        'Stop Manager',
        ButtonStyle.Danger
      ),
      this.messageUtils.createButton(
        'set-power',
        'Set Min Power',
        ButtonStyle.Secondary
      )
    )
  }
}

export default {
  data: {
    name: 'create_manager_panel',
    description: 'Create the alliance manager panel'
  },
  CommandHandlerClass: HandleAllianceManager
}
