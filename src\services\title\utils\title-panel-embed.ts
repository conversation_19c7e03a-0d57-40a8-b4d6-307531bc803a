import {
 type Client, EmbedBuilder, type TextChannel 
} from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'
import { setSettings } from 'utils/config/config-utils.js'

import type { InstanceConfig } from '../../../interfaces/config.js'
import { logger } from '../../../utils/logging/logger.js'

export async function updateTitlePanelEmbed(
  client: Client,
  instanceConfig: InstanceConfig,
  roleStatusMap: RoleStatusMap,
  titleToggleMap: TitleToggleMap
): Promise<void> {
  if (!instanceConfig.title?.channelId || !instanceConfig.title?.messageId) {
    return
  }

  try {
    const channel = (await client.channels.fetch(
      instanceConfig.title.channelId
    )) as TextChannel

    let message
    try {
      message = await channel.messages.fetch(instanceConfig.title.messageId)
    } catch (error) {
      message = await channel.send({
        content: 'Reinitializing title panel...',
        components: message?.components || []
      })

      setSettings(instanceConfig, 'title.messageId', message.id)
    }

    if (!message) {
      logger.error('Failed to create or fetch title panel message')
      return
    }

    const embed = createTitlePanelEmbed(
      instanceConfig,
      roleStatusMap,
      titleToggleMap
    )
    await message.edit({ embeds: [embed], components: message.components })
  } catch (error) {}
}

function createTitlePanelEmbed(
  instanceConfig: InstanceConfig,
  roleStatusMap: RoleStatusMap,
  titleToggleMap: TitleToggleMap
): EmbedBuilder {
  const titleStatus = titleToggleMap[instanceConfig.continent]
    ? 'Enabled'
    : 'Disabled'
  const roleStatus = roleStatusMap[instanceConfig.continent]

  const embed = new EmbedBuilder()
    .setTitle('Title Request Panel')
    .setDescription(
      `Title requests are currently: ${titleStatus}\n\n` +
        `**How to Use This Panel:**\n` +
        `1. Check the availability of Alchemist and Architect roles below.\n` +
        `2. If available, click the corresponding button to request the role.\n` +
        `3. Once granted, you'll receive the role for a limited time.\n` +
        `4. Use the "Expire Role" button when you're done to release the role.\n\n` +
        `**Important Notes:**\n` +
        `• Only one person can hold each role at a time.\n` +
        `• Be considerate and release the role promptly when finished.\n` +
        `• If you encounter any issues, please contact an admin.\n\n` +
        `**Title Requests Today:** ${instanceConfig.title.dailyRequests || 0}\n` +
        `**Total Requests:** ${instanceConfig.title.totalRequests || 0}`
    )
    .addFields([
      {
        name: 'Alchemist',
        value:
          roleStatus?.roleName === 'Alchemist' ? 'Unavailable' : 'Available',
        inline: true
      },
      {
        name: 'Architect',
        value:
          roleStatus?.roleName === 'Architect' ? 'Unavailable' : 'Available',
        inline: true
      }
    ])
    .setTimestamp()
    .setColor(titleStatus === 'Enabled' ? 0x00ff00 : 0xff0000)

  return embed
}
