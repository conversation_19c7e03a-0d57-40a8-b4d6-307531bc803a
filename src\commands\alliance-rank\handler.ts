import {
  type ChatInputCommandInteraction,
  ActionRowBuilder,
  StringSelectMenuBuilder,
  ButtonStyle
} from 'discord.js'
import { BaseCommandHandler } from 'handlers/commands/command-handler.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import type { AllianceMember } from '../../interfaces/alliance.js'
import type { InstanceConfig } from '../../interfaces/config.js'
import { fetchAllianceMembersRank } from '../../services/alliance/rank-service.js'
import type { ApiService } from '../../services/api/api-service.js'

const MEMBERS_PER_PAGE = 24

export class HandleAllianceRank extends BaseCommandHandler {
  private allMembers: AllianceMember[] = []
  private currentPage = 0

  constructor(
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap: TitleToggleMap,
    roleStatusMap: RoleStatusMap
  ) {
    super(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap
    )
  }

  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return
    await this.ensureToken()

    try {
      const rankedMembers = await fetchAllianceMembersRank(this.instanceConfig)
      this.allMembers = Object.values(rankedMembers).flat()

      if (this.allMembers.length === 0) {
        await this.interaction.reply('No alliance members found.')
        return
      }

      await this.displayMemberSelection()
    } catch (error) {
      await this.handleError(
        error,
        'An error occurred while fetching alliance members.'
      )
    }
  }

  private async displayMemberSelection(): Promise<void> {
    const totalPages = Math.ceil(this.allMembers.length / MEMBERS_PER_PAGE)
    const pageMembers = this.allMembers.slice(
      this.currentPage * MEMBERS_PER_PAGE,
      (this.currentPage + 1) * MEMBERS_PER_PAGE
    )

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId('select-member')
      .setPlaceholder('Select a member')
      .addOptions(this.createMemberOptions(pageMembers))

    const prevButton = this.messageUtils
      .createButton('alliance-prev', 'Previous', ButtonStyle.Secondary)
      .setDisabled(this.currentPage === 0)

    const nextButton = this.messageUtils
      .createButton('alliance-next', 'Next', ButtonStyle.Secondary)
      .setDisabled(this.currentPage === totalPages - 1)

    const actionRows = [
      new ActionRowBuilder<StringSelectMenuBuilder>().addComponents(selectMenu),
      this.messageUtils.createActionRow(prevButton, nextButton)
    ]

    const content = `Select a member to change their rank (Page ${this.currentPage + 1}/${totalPages}):`

    await this.interaction.reply({
      content,
      components: actionRows,
      ephemeral: false
    })
  }

  private createMemberOptions(members: AllianceMember[]) {
    const uniqueMembers = new Map<string, AllianceMember>();
      members.forEach(member => {
      uniqueMembers.set(member.kingdomId, member);
    });
    
    return Array.from(uniqueMembers.values()).map(member => ({
      label: `${member.name} (Rank ${member.rank})`,
      value: `${member.kingdomId}|${member.rank}`
    }));
  }
}

export default {
  data: {
    name: 'alliance-rank',
    description: 'Manage alliance member ranks'
  },
  CommandHandlerClass: HandleAllianceRank
}
