import { logger } from "../../../utils/logging/logger.js"
import type {
 SocketEventType, SocketEventPayload 
} from "../types.js"

type EventCallback = (detail: SocketEventPayload) => void

export class SocketEventDispatcher {
  private static instance: SocketEventDispatcher
  private eventTarget = new EventTarget()
  private listeners = new Map<string, Set<EventCallback>>()

  private constructor() {}

  public static getInstance(): SocketEventDispatcher {
    if (!SocketEventDispatcher.instance) {
      SocketEventDispatcher.instance = new SocketEventDispatcher()
    }
    return SocketEventDispatcher.instance
  }

  public dispatch<T extends SocketEventPayload>(type: SocketEventType, detail: T) {
    logger.debug(`[SocketEvents] Dispatching ${type} event`)

    this.eventTarget.dispatchEvent(new CustomEvent(type, { detail }))

    if (this.listeners.has(type)) {
      for (const listener of this.listeners.get(type)!) {
        try {
          listener(detail)
        } catch (error) {
          logger.error(`[SocketEvents] Error in listener for ${type}:`, error)
        }
      }
    }
  }

  public on<T extends SocketEventPayload>(type: SocketEventType, callback: (detail: T) => void): () => void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set())
    }

    this.listeners.get(type)!.add(callback as EventCallback)

    const listener = (event: Event) => callback((event as CustomEvent).detail)
    this.eventTarget.addEventListener(type, listener)

    return () => {
      this.off(type, callback)
      this.eventTarget.removeEventListener(type, listener)
    }
  }

  public off<T extends SocketEventPayload>(type: SocketEventType, callback: (detail: T) => void) {
    if (this.listeners.has(type)) {
      this.listeners.get(type)!.delete(callback as EventCallback)
    }
  }

  public offAll(type: SocketEventType) {
    if (this.listeners.has(type)) {
      this.listeners.delete(type)
    }
  }
}

export const socketEvents = SocketEventDispatcher.getInstance()

