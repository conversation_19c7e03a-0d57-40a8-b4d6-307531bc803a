/* eslint-disable @typescript-eslint/no-unused-vars */
import type { ButtonInteraction } from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types'

import type { InstanceConfig } from '../../../interfaces/config'
import type { ApiService } from '../../../services/api/api-service'
import { updateSkillsPanel } from '../../../services/skill/utils/skills-panel-embed'
import { logger } from '../../../utils/logging/logger'

export async function handleRefreshSkills(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap
): Promise<void> {
  try {
    await interaction.deferUpdate()

    await updateSkillsPanel(interaction.client, instanceConfig)

    await interaction.followUp({
      content: 'Global skills panel has been refreshed.',
      ephemeral: true
    })
  } catch (error) {
    logger.error('Error refreshing global skills panel:', error)
    await interaction.followUp({
      content: 'An error occurred while refreshing the global skills panel.',
      ephemeral: true
    })
  }
}
