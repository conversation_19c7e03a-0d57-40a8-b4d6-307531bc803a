import fs from 'fs/promises'
import {
 dirname, join 
} from 'path'
import { fileURLToPath } from 'url'

import { BotConfig } from '../../interfaces/config.js'
import { logger } from '../logging/logger.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

export async function loadConfig(): Promise<BotConfig> {
  try {
    const possiblePaths = [
      join(process.cwd(), 'config', 'config.json'),
      join(dirname(dirname(dirname(__dirname))), 'config', 'config.json'),
      join(__dirname, '..', '..', '..', 'config', 'config.json')
    ]

    let configPath: string | undefined
    for (const path of possiblePaths) {
      try {
        await fs.access(path)
        configPath = path
        break
      } catch {
        continue
      }
    }

    if (!configPath) {
      throw new Error(
        `Config file not found in any of the following locations:\n${possiblePaths.join('\n')}`
      )
    }

    const configData = await fs.readFile(configPath, 'utf8')
    const config = JSON.parse(configData)

    return {
      bot_token: config[0].bot_token,
      client_id: config[0].client_id,
      instances: config[0].instances
    }
  } catch (error) {
    logger.error('Error in loadConfig:', error)
    throw error
  }
}
