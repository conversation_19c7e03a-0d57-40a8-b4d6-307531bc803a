/* eslint-disable @typescript-eslint/no-unused-vars */
import type { ButtonInteraction } from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import type { InstanceConfig } from '../../../interfaces/config.js'
import type { ApiService } from '../../../services/api/api-service.js'
import { logger } from '../../../utils/logging/logger.js'

export async function handleAddKingdom(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap
): Promise<void> {
  try {
    await interaction.reply({
      content:
        'To add a kingdom, please use the `/add_kingdom` command. Usage: `/add_kingdom [kingdom_id]`',
      ephemeral: true
    })
    logger.info(
      `User ${interaction.user.id} was prompted to use /add_kingdom command`
    )
  } catch (error) {
    logger.error('Error in handleAddKingdom:', error)
    await interaction.reply({
      content:
        'An error occurred. Please try using the `/add_kingdom` command directly.',
      ephemeral: true
    })
  }
}
