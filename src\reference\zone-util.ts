// src/utils/zone-utils.ts
const LOC_PER_ZONE = 32
const GRID_SIZE = 256 // Total grid is 256x256
const LAND_SIZE = 8 // Each land is 8x8

function getRing(id) {
  return Math.ceil((Math.sqrt(id + 1) - 1) / 2)
}

function getMinRingID(r) {
  if (r == 0) {
    return 0
  }
  return Math.pow(2 * (r - 1) + 1, 2)
}

function getRingLegFromID(id) {
  if (id == 0) {
    return 0
  }

  const r = getRing(id)
  const m = getMinRingID(r)
  if (id >= m && id < m + 2 * r) {
    return 0
  } else if (id >= m + 2 * r && id < m + 4 * r) {
    return 1
  } else if (id >= m + 4 * r && id < m + 6 * r) {
    return 2
  } else if (id >= m + 6 * r) {
    return 3
  }
}

function getSpiralPos(id) {
  const ring = getRing(id)
  const min = getMinRingID(ring)
  const leg = getRingLegFromID(id)

  console.warn('id', id, leg)

  if (id === 0) {
    return [0, 0]
  }

  switch (leg) {
    case 0:
      return [-ring + (id - min + 1), ring]
    case 1:
      return [ring, ring - (id - min - (2 * ring - 1))]
    case 2:
      return [ring - (id - min - (4 * ring - 1)), -ring]
    case 3:
      return [-ring, -ring + (id - min - (6 * ring - 1))]
  }
}

export const zoneOnSpiral = (x: number, y: number, step: number) => {
  const spiralPos = getSpiralPos(step)
  console.warn('spiralPos:', spiralPos)
  const [offx, offy] = spiralPos
  const adjustedX = x + offx * LOC_PER_ZONE * 3
  const adjustedY = y + offy * LOC_PER_ZONE * 3

  const zones = getZones(adjustedX, adjustedY)
  //console.debug('zoneOnSpiral:', {
  //  input: { x, y, step },
  //  offset: { offx, offy },
  //  output: zones
  //})

  return zones
}

export const getLoc = () => {
  const x = (2203 % 64) * LOC_PER_ZONE
  const y = (2203 % 64) * LOC_PER_ZONE
  return x + y
}

export const getZone = (x, y, gridWidth = LOC_PER_ZONE, gridSize = 64) => {
  x = Math.floor(x / gridWidth)
  y = Math.floor(y / gridWidth) * gridSize
  return x + y
}

export const getZones = (x, y) => {
  const zone = getZone(x, y)
  const row = [zone - 1, zone, zone + 1]
  const rowAbove = row.map(z => z + 64)
  const rowBelow = row.map(z => z - 64)

  const allZones = [
    rowBelow[0],
    row[0],
    rowAbove[0],
    rowBelow[1],
    row[1],
    rowAbove[1],
    rowBelow[2],
    row[2],
    rowAbove[2]
  ].filter(z => z >= 0)

  return {
    center: zone >= 0 ? zone : null,
    zones: allZones
  }
}

/**
 *
 * @param {*} zones list of zones
 * @param {*} direction 0 for x 1 for y
 * @param {*} step how far to move, ideally 1 to simulate user panning the map
 */
export const pan = (zones, direction = 0, step = 1) => {
  const zonesPerRow = zones[1] - zones[0]
  const newZones =
    direction === 0
      ? zones.map(z => z + step)
      : zones.map(z => z + step * zonesPerRow)
  return newZones
}

export function getLandCoordinates(landId: number): { x: number; y: number } {
  const adjustedId = landId - 100000
  // Calculate bottom-left corner first
  const cornerX = (adjustedId % GRID_SIZE) * LAND_SIZE
  const cornerY = Math.floor(adjustedId / GRID_SIZE) * LAND_SIZE

  return {
    x: cornerX + LAND_SIZE / 2,
    y: cornerY + LAND_SIZE / 2
  }
}

export function getZonesForLand(landId: number): number[] {
  const { x, y } = getLandCoordinates(landId)
  return getZones(x, y).zones
}
