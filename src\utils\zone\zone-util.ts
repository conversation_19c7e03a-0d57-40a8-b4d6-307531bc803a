const LOC_PER_ZONE = 32
const GRID_SIZE = 256 // Total grid is 256x256
const LAND_SIZE = 8 // Each land is 8x8

export function getRing(id: number): number {
  return Math.ceil((Math.sqrt(id + 1) - 1) / 2)
}

export function getMinRingID(r: number): number {
  if (r === 0) {
    return 0
  }
  return Math.pow(2 * (r - 1) + 1, 2)
}

export function getRingLegFromID(id: number): number {
  if (id === 0) {
    return 0
  }

  const r = getRing(id)
  const m = getMinRingID(r)
  if (id >= m && id < m + 2 * r) {
    return 0
  } else if (id >= m + 2 * r && id < m + 4 * r) {
    return 1
  } else if (id >= m + 4 * r && id < m + 6 * r) {
    return 2
  } else if (id >= m + 6 * r) {
    return 3
  }
  
  return 0 // Default case
}

export function getSpiralPos(id: number): [number, number] {
  const ring = getRing(id)
  const min = getMinRingID(ring)
  const leg = getRingLegFromID(id)

  if (id === 0) {
    return [0, 0]
  }

  switch (leg) {
    case 0:
      return [-ring + (id - min + 1), ring]
    case 1:
      return [ring, ring - (id - min - (2 * ring - 1))]
    case 2:
      return [ring - (id - min - (4 * ring - 1)), -ring]
    case 3:
      return [-ring, -ring + (id - min - (6 * ring - 1))]
    default:
      return [0, 0]
  }
}

export const zoneOnSpiral = (x: number, y: number, step: number) => {
  const spiralPos = getSpiralPos(step)
  const [offx, offy] = spiralPos
  const adjustedX = x + offx * LOC_PER_ZONE * 3
  const adjustedY = y + offy * LOC_PER_ZONE * 3

  return getZones(adjustedX, adjustedY)
}

export const getZone = (
  x: number, 
  y: number, 
  gridWidth = LOC_PER_ZONE, 
  gridSize = 64
): number => {
  const zoneX = Math.floor(x / gridWidth)
  const zoneY = Math.floor(y / gridWidth) * gridSize
  return zoneX + zoneY
}

export const getZones = (x: number, y: number) => {
  const zone = getZone(x, y)
  const row = [zone - 1, zone, zone + 1]
  const rowAbove = row.map(z => z + 64)
  const rowBelow = row.map(z => z - 64)

  const allZones = [
    rowBelow[0],
    row[0],
    rowAbove[0],
    rowBelow[1],
    row[1],
    rowAbove[1],
    rowBelow[2],
    row[2],
    rowAbove[2]
  ].filter(z => z >= 0)

  return {
    center: zone >= 0 ? zone : null,
    zones: allZones
  }
}

export function getLandCoordinates(landId: number): { x: number; y: number } {
  const adjustedId = landId - 100000
  // Calculate bottom-left corner first
  const cornerX = (adjustedId % GRID_SIZE) * LAND_SIZE
  const cornerY = Math.floor(adjustedId / GRID_SIZE) * LAND_SIZE

  return {
    x: cornerX + LAND_SIZE / 2,
    y: cornerY + LAND_SIZE / 2
  }
}

export function getZonesForLand(landId: number): number[] {
  const { x, y } = getLandCoordinates(landId)
  return getZones(x, y).zones
}
