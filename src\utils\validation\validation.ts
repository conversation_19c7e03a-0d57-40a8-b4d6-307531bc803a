import { InstanceConfig } from '../../interfaces/config.js'

export function isValidCoordinates(coords: string): boolean {
  return /^\d{1,4},\d{1,4}$/.test(coords)
}

export function isValidDiscordId(id: string): boolean {
  return /^\d{17,19}$/.test(id)
}

export function isValidSkillCode(code: string): boolean {
  const validCodes = ['1', '2', '3', '4', '5', '6', '7', '8']
  return validCodes.includes(code)
}

export function sanitizeInput(input: string): string {
  return input.replace(/[<>]/g, '').trim()
}

export function validateInstanceConfig(
  config: unknown
): config is InstanceConfig {
  if (!config || typeof config !== 'object') return false

  const requiredFields = [
    'username',
    'password',
    'guild_id',
    'enabledCommands',
    'adminRole',
    'continent',
    'allyID',
    'verify'
  ] as const

  for (const field of requiredFields) {
    if (!(field in (config as Record<string, unknown>))) {
      return false
    }
  }

  const typedConfig = config as InstanceConfig

  if (
    !Array.isArray(typedConfig.enabledCommands) ||
    !Array.isArray(typedConfig.allyID)
  ) {
    return false
  }

  if (typeof typedConfig.verify !== 'object' || typedConfig.verify === null) {
    return false
  }

  if (
    !('roleName' in typedConfig.verify) ||
    !('channelId' in typedConfig.verify)
  ) {
    return false
  }

  return true
}
