@cryptokairo ➜ /workspaces/typescript-conversion (socket) $ pnpm run start:dev
Debugger attached.

> lok-discord-bot@1.0.0 start:dev /workspaces/typescript-conversion
> node dist/index.js

Debugger attached.
2025-05-05 00:37:03 [info]: [ClientInit] Logging in for Continent 11
2025-05-05 00:37:03 [info]: [loginService] Attempting login for Continent 11
2025-05-05 00:37:03 [debug]: [initializeApi] Creating instance for continent:
{
  "continent": 11,
  "stackTrace": "Error\n    at initializeApi (file:///workspaces/typescript-conversion/dist/index.js:37344:17)\n    at login (file:///workspaces/typescript-conversion/dist/index.js:37054:5)\n    at initializeClient (file:///workspaces/typescript-conversion/dist/index.js:68923:31)\n    at main (file:///workspaces/typescript-conversion/dist/index.js:69010:30)"
}
2025-05-05 00:37:04 [debug]: API Response Details:
{
  "method": "POST",
  "url": "https://api-lok-live.leagueofkingdoms.com/api/auth/login",
  "data": {
    "result": true,
    "verisoul": false,
    "user": {
      "_id": "66157e26b2de2be7c96f3072",
      "userKey": "<EMAIL>",
      "authType": "email",
      "agree": true
    },
    "property": {
      "crystal": "25918",
      "purchase": "0",
      "mint": 0,
      "unmint": 0,
      "vip": 8,
      "kingdom": 22
    },
    "lstProtect": "WyIvYXBpL2tpbmdkb20vd29ybGQvY2hhbmdlIiwiL2FwaS9raW5nZG9tL2hvc3BpdGFsL3JlY292ZXIiLCIvYXBpL2tpbmdkb20vcmVzb3VyY2UvaGFydmVzdCIsIi9hcGkva2luZ2RvbS9idWlsZGluZy91cGdyYWRlIiwiL2FwaS9raW5nZG9tL2J1aWxkaW5nL2RlbW9saXNoIiwiL2FwaS9raW5nZG9tL3RyZWFzdXJlL2V4Y2hhbmdlIiwiL2FwaS9raW5nZG9tL3RyZWFzdXJlL3VwZ3JhZGUiLCIvYXBpL2tpbmdkb20vdHJlYXN1cmUvc2tpbGwvdXBncmFkZSIsIi9hcGkva2luZ2RvbS90YXNrL3NwZWVkdXAiLCIvYXBpL2tpbmdkb20vaGVhbC9zcGVlZHVwIiwiL2FwaS9raW5nZG9tL2hlYWwvaW5zdGFudCIsIi9hcGkva2luZ2RvbS92aXBzaG9wL2J1eSIsIi9hcGkva2luZ2RvbS92aXAvY2xhaW0iLCIvYXB...
2025-05-05 00:37:04 [info]: Updated instanceConfig setting: token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.isuZmFp_7_HLTA0P6eAyzsgzPVRtaDUbRqhaQTjOHNM"
2025-05-05 00:37:04 [info]: Updated instanceConfig setting: regionHash = "IjUuOTg2NzcwMzY0NjU3NzYzNS0uMHgzMDRkaDM3NjFnYTMuLTAi"
2025-05-05 00:37:04 [info]: Updated instanceConfig setting: xorKey = ".0x304dh3761ga3."
2025-05-05 00:37:04 [debug]: [loginService] Updated instanceConfig:
{
  "instanceConfig": {
    "username": "<EMAIL>",
    "password": "TitleGod1!",
    "guild_id": "1139934778448166994",
    "enabledCommands": [
      "land",
      "title_toggle",
      "request_title",
      "create_title_panel",
      "verify",
      "shrine_timer",
      "rotate",
      "staker",
      "search",
      "player_data",
      "update_data",
      "summary",
      "create_skills_panel",
      "global-skill",
      "refresh-skills",
      "mail",
      "alliance-rank",
      "add_kingdom",
      "create_manager_panel",
      "create_rotate_panel",
      "set_max_kick",
      "set_power",
      "create_eject_panel"
    ],
    "adminRole": "KING",
    "continent": 11,
    "allyID": [
      "62cc39fe6c00f431bd65213a"
    ],
    "verify": {
      "roleName": "Verified",
      "channelId": "1139934779639332967"
    },
    "title": {
      "channelId": "1139934779907788891",
      "messageId": null,
      "dailyRequests": 0,
      "totalRequests": 0,
      "la...
2025-05-05 00:37:04 [info]: [ClientInit] Successfully logged in for Continent 11
2025-05-05 00:37:04 [info]: [ClientInit] Entering kingdom for Continent 11
2025-05-05 00:37:04 [info]: [KingdomService] Entering kingdom for Continent 11
2025-05-05 00:37:05 [debug]: API Response Details:
{
  "method": "POST",
  "url": "https://api-lok-live.leagueofkingdoms.com/api/kingdom/enter",
  "data": {
    "result": true,
    "kingdom": {
      "wall": {
        "maxDurability": 15000,
        "durability": 16000,
        "fireType": 0,
        "lastRepairDate": "2024-04-28T08:55:22.496Z"
      },
      "bf": {
        "worldId": 100002,
        "entered": "2025-05-05T00:06:50.538Z",
        "status": 1
      },
      "boost": {
        "total": [
          [
            11,
            4,
            4
          ],
          [
            12,
            1,
            125000
          ],
          [
            13,
            1,
            1200000
          ],
          [
            21,
            1,
            2100000
          ],
          [
            22,
            1,
            2100000
          ],
          [
            23,
            1,
            2100000
          ],
          [
            24,
            1,
            2100000
          ],
          [
     ...
2025-05-05 00:37:05 [info]: [KingdomService] Successfully entered kingdom C11 Rebound (66157e2cb5a1efcad045fcdd)
2025-05-05 00:37:05 [debug]: [KingdomService] Kingdom data:
{
  "networks": {
    "api": "https://api-lok-live.leagueofkingdoms.com/api",
    "kingdoms": [
      "https://sock-lok-live.leagueofkingdoms.com/socket.io/"
    ],
    "fields": [
      "https://socf-lok-live.leagueofkingdoms.com/socket.io/"
    ],
    "chats": [
      "https://socc-lok-live.leagueofkingdoms.com/socket.io/"
    ]
  },
  "kingdom": {
    "id": "66157e2cb5a1efcad045fcdd",
    "name": "C11 Rebound",
    "loc": [
      100002,
      36,
      1474
    ]
  }
}
2025-05-05 00:37:05 [info]: Updated instanceConfig setting: kingdomData = {"result":true,"kingdom":{"wall":{"maxDurability":15000,"durability":16000,"fireType":0,"lastRepairDate":"2024-04-28T08:55:22.496Z"},"bf":{"worldId":100002,"entered":"2025-05-05T00:06:50.538Z","status":1},"boost":{"total":[[11,4,4],[12,1,125000],[13,1,1200000],[21,1,2100000],[22,1,2100000],[23,1,2100000],[24,1,2100000],[16,4,6],[202,4,800],[68,2,0.44999999999999996],[25,4,4],[118,1,8],[66,2,0],[14,2,0.39],[29,1,252000],[30,2,0.29],[31,2,0.245],[32,1,10],[19,1,75000],[20,1,112500],[26,1,15500],[27,2,0.25],[28,2,0.25],[87,2,0.3],[42,1,13],[69,2,0.06],[212,1,4],[213,1,1],[215,1,1],[4,1,21100],[8,1,168800],[39,2,0.38],[5,1,24700],[9,1,197600],[40,2,0.24000000000000002],[2,1,11400],[6,1,91200],[37,2,0.38],[82,1,4600],[83,2,0],[3,1,13400],[7,1,107200],[38,2,0.38],[2,2,0.6699999999999999],[50,2,0.61],[51,2,0.6],[52,2,0.69],[3,2,0.6599999999999999],[4,2,0.65],[54,2,0.51],[53,2,0.5299999999999999],[55,2,0.51],[5,2,0.59],[6,2,0.05],[7,2,0.05],[8,2,0.05],[9,2,0.15],[56,2,0.5199999999999999],[57,2,0.51],[58,2,0.51],[59,2,0.06999999999999999],[60,2,0.03],[61,2,0.05],[70,3,1],[71,3,1],[72,3,1],[88,2,0.03],[89,2,0.03],[90,2,0.03],[91,2,0.03],[92,2,0.03],[93,2,0.03],[94,2,-0.06],[95,2,-0.06],[96,2,-0.03],[20,2,0.01],[12,2,0.01],[35,2,0.52],[109,2,0.8700000000000001],[36,2,0.35],[119,2,0.42000000000000004],[33,1,200],[34,1,50],[19,2,0.15000000000000002],[117,2,0.15],[17,3,1],[11,1,1],[13,2,0.05],[100,1,10]]},"stats":{"battle":{"victory":0,"defeated":0,"kill":0,"death":0},"monster":{"attack":41,"kill":37},"economy":{"gathering":565416},"troops":{"heal":2091,"train":127874},"alliance":{"donate":0,"help":0},"total":{"crystal":25918,"purchase":0,"medals":56000,"max_purchase":0,"refund":0,"mint":[],"unmint":[]}},"vip":{"level":8,"point":24530,"lastClaimTime":"2025-05-05T00:09:53.548Z"},"actionPoint":{"value":150,"lastUpdated":"2025-03-24T02:58:11.367Z"},"freeChest":{"silver":{"num":0,"next":"2025-03-31T08:36:00.703Z"},"gold":{"next":"2025-04-02T05:50:47.962Z"},"platinum":{"next":"2025-04-07T05:50:48.339Z"}},"engage":{"comback":false,"nopay":true},"option":{"push":1,"push_war":1,"alarm":1,"gift":1},"ban":{"face":false,"chat":false},"klaytn":{"invite":{"card":3,"total":0,"res":0},"klay":{"claimed":0,"unclaimed":0}},"hospital":{"checking":false,"lastUpdated":"2025-03-24T03:12:18.144Z"},"deletedInfo":{"loc":[]},"invade":{"win":0,"defeat":0,"difficulty":1,"wave":1,"status":0},"dsaVip":{"level":0,"pledging":0,"lastLevelingDate":"2025-05-05T00:00:00.000Z"},"_id":"66157e2cb5a1efcad045fcdd","level":22,"resources":[222283.40000000224,961515.5999999996,555479.6000000006,478492.5999999996],"crystal":182,"power":4053777,"worldId":11,"alliancePoint":38400,"numDonation":20,"todayHP":100,"todayRP":0,"todayDonation":0,"faceCode":0,"selectedTreasure":0,"saveTreasures":[[{"slot":0,"itemId":"66171446cb90d116553f16b7","_id":"66157e3365793667de33c605"},{"slot":1,"itemId":"6615884acb90d116556d3985","_id":"66157e3365793667de33c606"},{"slot":2,"itemId":"66158329cb90d11655cbad69","_id":"6617144a30a4e49037f13e35"},{"slot":3,"itemId":"67b541421f14317c49c05ebe","_id":"67a9eef230efc1d1cced2cc3"}],[{"slot":0,"_id":"66157e349824a86f273b7116"},{"slot":1,"_id":"66157e349824a86f273b7117"},{"slot":2,"_id":"6617144a30a4e49037f13e37"},{"slot":3,"_id":"67a9eef230efc1d1cced2cc4"}],[{"slot":0,"_id":"66157e349824a86f273b7118"},{"slot":1,"_id":"66157e349824a86f273b7119"},{"slot":2,"_id":"6617144a30a4e49037f13e38"},{"slot":3,"_id":"67a9eef230efc1d1cced2cc5"}],[{"slot":0,"_id":"66157e349824a86f273b711a"},{"slot":1,"_id":"66157e349824a86f273b711b"},{"slot":2,"_id":"6617144a30a4e49037f13e39"},{"slot":3,"_id":"67a9eef230efc1d1cced2cc6"}]],"saveTroops":[[],[],[],[]],"tutorialType":1,"tutorials":[],"todayLogin":true,"yesterdayLogin":false,"medal":90000,"current_medal":0,"congressTitle":101,"mainLandId":0,"blockIds":[],"researches":[{"code":30102001,"level":5,"_id":"66158149f7ba4472cf3360db"},{"code":30101001,"level":5,"_id":"6615819f9a9317f5e05deb80"},{"code":30101002,"level":5,"_id":"661581a59d80ca3313ca701f"},{"code":30101003,"level":5,"_id":"661581caef3ccba5ef1527dc"},{"code":30102002,"level":5,"_id":"661581d49a9317f5e05ef8f8"},{"code":30102003,"level":5,"_id":"66158472f7ba4472cf6aa6f3"},{"code":30101005,"level":3,"_id":"6615848dbab9a8015212b3de"},{"code":30101004,"level":3,"_id":"661584c19a9317f5e0740b39"},{"code":30101006,"level":3,"_id":"661584c649fd27d578387148"},{"code":30102004,"level":5,"_id":"6615850791bfb03ac0631e91"},{"code":30102005,"level":2,"_id":"66158539f7ba4472cf767085"},{"code":30102006,"level":2,"_id":"6615854291bfb03ac0658b80"},{"code":30102007,"level":2,"_id":"6615854b91bfb03ac066b119"},{"code":30102008,"level":4,"_id":"67b3c1be142e9c2243b3f92a"},{"code":30101007,"level":3,"_id":"67b6f3010d238a094aa2f714"},{"code":30101008,"level":3,"_id":"67b6f340478733c86cbd946e"},{"code":30101009,"level":3,"_id":"67b6f37ed04fab4beda04937"},{"code":30101010,"level":3,"_id":"67b6f6a6839f0402310471b9"},{"code":30101011,"level":2,"_id":"67b6f6e0821050d86a2e8d2b"},{"code":30101012,"level":2,"_id":"67b6f76741dcb3617b9a31f4"},{"code":30101013,"level":3,"_id":"67b84103434f2f352cf44018"},{"code":30101014,"level":1,"_id":"67b84a38508ba2b9593ad384"},{"code":30101015,"level":1,"_id":"67b84ba9217aa110c08273b8"},{"code":30101016,"level":1,"_id":"67b84e4784f4dcbd3efd7c3a"},{"code":30101017,"level":2,"_id":"67b84ec0bc6fefd8f960c613"},{"code":30101018,"level":2,"_id":"67b84fa18acff30f9d946cae"},{"code":30101019,"level":2,"_id":"67b85088f92ff141930cdab6"},{"code":30101020,"level":2,"_id":"67b85255bc6fefd8f9b050c4"},{"code":30101021,"level":2,"_id":"67b853b83e9e8c8ccb2ea2fa"},{"code":30101022,"level":2,"_id":"67b85791285650f3e19d3354"},{"code":30101023,"level":3,"_id":"67b85e3c1de02aed48fcc20c"},{"code":30101024,"level":3,"_id":"67b867dacc8d8fd8371361ff"},{"code":30101025,"level":2,"_id":"67bba6de6b58a1a8b819c6ae"}],"buildings":[{"position":1,"code":40100101,"state":1,"level":22,"builded":"2025-04-23T03:32:56.720Z","lastUpgraded":"2025-04-23T03:32:56.720Z","lastHarvested":null,"taskId":null},{"position":2,"code":40100102,"state":1,"level":17,"builded":"2025-02-12T20:31:37.871Z","lastUpgraded":"2025-02-12T20:31:37.871Z","lastHarvested":null,"taskId":null},{"position":3,"code":40100103,"state":1,"level":18,"builded":"2025-02-12T20:34:55.658Z","lastUpgraded":"2025-02-12T20:34:55.658Z","lastHarvested":null,"taskId":null},{"position":4,"code":40100104,"state":1,"level":18,"builded":"2025-04-03T21:09:12.927Z","lastUpgraded":"2025-04-03T21:09:12.927Z","lastHarvested":null,"taskId":null},{"position":5,"code":40100105,"state":1,"level":21,"builded":"2025-04-04T14:29:55.034Z","lastUpgraded":"2025-04-04T14:29:55.034Z","lastHarvested":null,"taskId":null},{"position":6,"code":40100106,"state":1,"level":19,"builded":"2025-02-15T16:58:05.538Z","lastUpgraded":"2025-02-15T16:58:05.538Z","lastHarvested":"2025-03-24T03:12:18.144Z","taskId":null,"param":{"wounded":[],"cured":[]}},{"position":7,"code":40100107,"state":1,"level":2,"builded":"2024-04-09T17:43:15.260Z","lastUpgraded":"2024-04-09T18:03:18.881Z","lastHarvested":null,"taskId":null},{"position":8,"code":40100108,"state":1,"level":21,"builded":"2025-02-25T03:00:54.578Z","lastUpgraded":"2025-02-25T03:00:54.578Z","lastHarvested":null,"taskId":null},{"position":9,"code":40100109,"state":1,"level":17,"builded":"2025-03-29T09:35:26.318Z","lastUpgraded":"2025-03-29T09:35:26.318Z","lastHarvested":null,"taskId":null},{"position":10,"code":40100110,"state":1,"level":1,"builded":"2024-04-09T17:43:15.518Z","lastUpgraded":"2024-04-09T17:43:15.518Z","lastHarvested":null,"taskId":null},{"position":101,"code":40100204,"state":1,"level":10,"builded":"2025-02-21T10:04:50.079Z","lastUpgraded":"2025-02-21T10:04:50.079Z","lastHarvested":"2025-05-05T00:29:23.645Z","param":{"prod":7560,"cap":35280.00000000001},"harvested":35280.00000000001},{"position":102,"code":40100205,"state":1,"level":10,"builded":"2025-02-21T10:03:38.548Z","lastUpgraded":"2025-02-21T10:03:38.548Z","lastHarvested":"2025-05-05T00:29:23.782Z","param":{"prod":7380,"cap":37679.99999999999},"harvested":37679.99999999999},{"position":103,"code":40100205,"state":1,"level":9,"builded":"2025-02-21T10:06:00.428Z","lastUpgraded":"2025-02-21T10:06:00.428Z","lastHarvested":"2025-05-05T00:29:23.828Z","param":{"prod":4428,"cap":22607.999999999996},"harvested":22607.999999999996},{"position":104,"code":40100202,"state":1,"level":20,"builded":"2025-02-18T00:02:51.960Z","lastUpgraded":"2025-02-18T00:02:51.960Z","lastHarvested":"2025-05-05T00:29:23.891Z","param":{"prod":17780,"cap":82320.00000000001},"harvested":82320.00000000001},{"position":105,"code":40100201,"state":1,"level":20,"builded":"2025-02-19T09:28:09.210Z","lastUpgraded":"2025-02-19T09:28:09.210Z"},{"position":106,"code":40100203,"state":1,"level":18,"builded":"2025-03-09T12:13:33.487Z","lastUpgraded":"2025-03-09T12:13:33.487Z","lastHarvested":"2025-05-05T00:29:23.907Z","param":{"prod":15686.000000000002,"cap":72912.00000000001},"harvested":72912.00000000001},{"position":107,"code":40100204,"state":1,"level":21,"builded":"2025-02-21T18:51:07.744Z","lastUpgraded":"2025-02-21T18:51:07.744Z","lastHarvested":"2025-05-05T00:29:23.869Z","param":{"prod":18900,"cap":88200.00000000001},"harvested":88200.00000000001},{"position":108,"code":40100202,"state":1,"level":5,"builded":"2025-02-23T22:53:54.181Z","lastUpgraded":"2025-02-23T22:53:54.181Z","lastHarvested":"2025-05-05T00:29:22.963Z","param":{"prod":2540,"cap":11760.000000000002},"harvested":11760.000000000002},{"position":109,"code":40100203,"state":1,"level":12,"builded":"2025-03-15T10:26:21.862Z","lastUpgraded":"2025-03-15T10:26:21.862Z","lastHarvested":"2025-05-05T00:29:23.882Z","param":{"prod":9614.000000000002,"cap":44688.00000000001},"harvested":44688.00000000001},{"position":110,"code":40100205,"state":1,"level":21,"builded":"2025-03-31T15:18:30.557Z","lastUpgraded":"2025-03-31T15:18:30.557Z","lastHarvested":"2025-05-05T00:29:23.970Z","param":{"prod":18450,"cap":94199.99999999999},"harvested":94199.99999999999},{"position":111,"code":40100201,"state":1,"level":8,"builded":"2024-04-09T17:57:39.730Z","lastUpgraded":"2024-04-09T18:26:46.069Z"},{"position":112,"code":40100204,"state":1,"level":12,"builded":"2025-02-21T09:30:15.838Z","lastUpgraded":"2025-02-21T09:30:15.838Z","lastHarvested":"2025-05-05T00:29:23.876Z","param":{"prod":9576,"cap":44688.00000000001},"harvested":44688.00000000001},{"position":113,"code":40100204,"state":1,"level":12,"builded":"2025-02-21T09:30:20.575Z","lastUpgraded":"2025-02-21T09:30:20.575Z","lastHarvested":"2025-05-05T00:29:23.864Z","param":{"prod":9576,"cap":44688.00000000001},"harvested":44688.00000000001},{"position":114,"code":40100205,"state":1,"level":9,"builded":"2025-02-21T12:08:33.503Z","lastUpgraded":"2025-02-21T12:08:33.503Z","lastHarvested":"2025-05-05T00:29:23.808Z","param":{"prod":4428,"cap":22607.999999999996},"harvested":22607.999999999996},{"position":115,"code":40100205,"state":1,"level":12,"builded":"2025-02-23T17:00:27.757Z","lastUpgraded":"2025-02-23T17:00:27.757Z","lastHarvested":"2025-05-05T00:29:23.679Z","param":{"prod":9348,"cap":47727.99999999999},"harvested":47727.99999999999},{"position":116,"code":40100204,"state":1,"level":10,"builded":"2025-02-21T10:22:02.497Z","lastUpgraded":"2025-02-21T10:22:02.497Z","lastHarvested":"2025-05-05T00:29:23.844Z","param":{"prod":7560,"cap":35280.00000000001},"harvested":35280.00000000001},{"position":117,"code":40100205,"state":1,"level":11,"builded":"2025-02-21T11:19:46.700Z","lastUpgraded":"2025-02-21T11:19:46.700Z","lastHarvested":"2025-05-05T00:29:23.960Z","param":{"prod":8364,"cap":42703.99999999999},"harvested":42703.99999999999},{"position":118,"code":40100205,"state":1,"level":11,"builded":"2025-02-23T16:45:33.829Z","lastUpgraded":"2025-02-23T16:45:33.829Z","lastHarvested":"2025-05-05T00:29:23.683Z","param":{"prod":8364,"cap":42703.99999999999},"harvested":42703.99999999999},{"position":119,"code":40100202,"state":1,"level":11,"builded":"2025-02-21T08:56:36.507Z","lastUpgraded":"2025-02-21T08:56:36.507Z","lastHarvested":"2025-05-05T00:29:23.771Z","param":{"prod":8636,"cap":39984.00000000001},"harvested":39984.00000000001},{"position":120,"code":40100203,"state":1,"level":11,"builded":"2025-02-21T09:03:58.587Z","lastUpgraded":"2025-02-21T09:03:58.587Z","lastHarvested":"2025-05-05T00:29:23.933Z","param":{"prod":8602,"cap":39984.00000000001},"harvested":39984.00000000001}],"troops":[{"code":50100101,"amount":27800,"_id":"661582d3ee6df0ee62f994c0"},{"code":50100201,"amount":9300,"_id":"661582db38aef96c777e866e"},{"code":50100301,"amount":35200,"_id":"6615830f67f6291ba5b25c3e"},{"code":50100102,"amount":28198,"_id":"67b84bbca20b5cc5c8dd2343"},{"code":50100202,"amount":18722,"_id":"67b84fb01de02aed480ca8e5"},{"code":50100302,"amount":21154,"_id":"67b850243e9e8c8ccbaf3012"}],"supports":[],"lastDonationCheckTime":"2025-05-05T00:33:15.871Z","treasure":[{"slot":0,"itemId":"66171446cb90d116553f16b7","_id":"66157e3365793667de33c605"},{"slot":1,"itemId":"6615884acb90d116556d3985","_id":"66157e3365793667de33c606"},{"slot":2,"itemId":"66158329cb90d11655cbad69","_id":"6617144a30a4e49037f13e35"},{"slot":3,"_id":"67a9eef230efc1d1cced2cc3","itemId":"67b541421f14317c49c05ebe"}],"bookmarks":[],"registered":"2024-04-09T17:43:08.667Z","skills":[],"userId":"66157e26b2de2be7c96f3072","name":"C11 Rebound","__v":1,"fieldObjectId":"6818011ab18807f81d3b491a","lastLogined":"2025-05-05T00:37:03.538Z","numSeriesLogin":1,"lordId":"66157e3365793667de33c5e6","allianceId":"60f53dea84d14075416cb5e7","loc":[100002,36,1474],"alliance":{"_id":"60f53dea84d14075416cb5e7","name":"LIBERATORS2","tag":"LWA2","power":23114138577,"leaderKingdomId":"66157e2cb5a1efcad045fcdd","description":"LIBERATORS AVENGERS\n<color=#FFFF00><b>☆☆☆☆☆☆☆☆</b></color>\n<color=#FF0000><b>Welcome to the Family</b></color>\n<color=#FFFF00><b>☆☆☆☆☆☆☆☆</b></color>\n<color=#FF0000><b>Continent Rules</b></color>\n◈ DO NOT leave any unfinished mines\n◈ DSA can be farm all locations but for DSA level must check your eligibility\n◈ Cmines levels must also check your eligibility (based on CVC ranking or staking rights)\n◈ Please refer to RULES channel in C11 discord if you are unclear.\n<color=#FF0000><b> 3 times mistake and breaking the rules will have the maximum punishment which is BURN & LOOT</b></color>\n<color=#FFFF00><b>Requirements</b></color>\n◈ Only active players\n◈ Teleport to HIVE unless permission given by King's Team\n◈ Join Continent 11 discord: ask R4/R5 for invites\n◈ Participate in all alliance activities\n◈ Be active, 24H offline = kick\n","numMembers":97,"maxMembers":100,"flag":{"backShape":16,"patternShape":0},"option":{"minPower":0,"minLevel":1,"allowType":2},"langCode":"all","level":7,"exp":54919350,"alliancePoint":107830400,"worldId":11,"nft":{"index":1,"num":3}},"lord":{"level":12,"xp":20910,"point":1},"match3":{"lifeMax":10,"regenInterval":300000},"logging":false,"dragoCountSnap":0},"dbTime":"2025-05-05T00:37:05.196Z","blockIds":[],"captcha":null,"networks":{"api":"https://api-lok-live.leagueofkingdoms.com/api","kingdoms":["https://sock-lok-live.leagueofkingdoms.com/socket.io/"],"fields":["https://socf-lok-live.leagueofkingdoms.com/socket.io/"],"chats":["https://socc-lok-live.leagueofkingdoms.com/socket.io/"]},"ct":"US"}
2025-05-05 00:37:05 [info]: [ClientInit] Successfully entered kingdom for Continent 11
2025-05-05 00:37:05 [info]: [ClientInit] Initializing socket service for Continent 11
2025-05-05 00:37:05 [info]: [WsInstance] Initializing socket service for Continent 11
2025-05-05 00:37:05 [debug]: [SocketService] socc state changed to connecting
2025-05-05 00:37:05 [debug]: [SocketService] socc state changed to connected
2025-05-05 00:37:05 [info]: [SocketConnection] Connected to socc WebSocket
2025-05-05 00:37:05 [debug]: [SocketService] socc state changed to initialized
2025-05-05 00:37:05 [debug]: [SocketService] Sending to socc:/chat/enter
2025-05-05 00:37:05 [debug]: [SocketService] sock state changed to connecting
2025-05-05 00:37:05 [debug]: [SocketService] sock state changed to connected
2025-05-05 00:37:05 [info]: [SocketConnection] Connected to sock WebSocket
2025-05-05 00:37:05 [debug]: [SocketService] sock state changed to initialized
2025-05-05 00:37:05 [debug]: [SocketService] Sending to sock:/kingdom/enter
2025-05-05 00:37:05 [debug]: [SocketService] socf state changed to connecting
2025-05-05 00:37:05 [debug]: [SocketService] socf state changed to connected
2025-05-05 00:37:05 [info]: [SocketConnection] Connected to socf WebSocket
2025-05-05 00:37:05 [debug]: [SocketService] socf state changed to initialized
2025-05-05 00:37:05 [debug]: [SocketService] Sending to socf:/field/enter/v3
2025-05-05 00:37:05 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:05 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
2025-05-05 00:37:05 [info]: [ClientInit] Socket service initialized for Continent 11
2025-05-05 00:37:05 [info]: Successfully initialized client for Continent 11
2025-05-05 00:37:05 [info]: Loading commands...
2025-05-05 00:37:05 [info]: Successfully loaded 20 commands
2025-05-05 00:37:05 [info]: Successfully loaded 20 commands
2025-05-05 00:37:06 [debug]: [SocketMessageHandler] Sample kingdom objects:
{
  "0": {
    "id": "6818011ab18807f81d3b491a",
    "loc": [
      100002,
      36,
      1474
    ],
    "level": 22,
    "name": "C11 Rebound",
    "alliance": "LWA2"
  }
}
2025-05-05 00:37:06 [debug]: [SocketEvents] Dispatching field:objects event
2025-05-05 00:37:06 [info]: Successfully deployed 20 commands for guild 1139934778448166994 (Continent 11)
2025-05-05 00:37:06 [info]: Bot is now online!
2025-05-05 00:37:06 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:06 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
(node:21188) Warning: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
(Use `node --trace-warnings ...` to show where the warning was created)
2025-05-05 00:37:20 [info]: Updated instanceConfig setting: eject.messageId = "1368748366250377286"
2025-05-05 00:37:20 [info]: Updated instanceConfig setting: eject.channelId = "1346930620164472832"
2025-05-05 00:37:38 [debug]: [EjectService] Subscribed to field objects events
2025-05-05 00:37:38 [debug]: [SearchStrategy] Initialized with 9 spiral steps
2025-05-05 00:37:38 [info]: [ZoneSearchService] Initialized for continent 11
2025-05-05 00:37:38 [info]: [EjectService] Zone search service initialized
2025-05-05 00:37:38 [info]: [ZoneSearchService] Started for continent 11
2025-05-05 00:37:38 [info]: [EjectService] Zone search started
2025-05-05 00:37:38 [info]: [EjectService] Started for continent 11
2025-05-05 00:37:38 [info]: Updated instanceConfig setting: eject.enabled = true
2025-05-05 00:37:38 [info]: CVC Eject service enabled for continent 11
2025-05-05 00:37:39 [debug]: [ZoneSearchService] Initializing with location: [100002, 36, 1474]
2025-05-05 00:37:39 [debug]: [ZoneManager] Initialized zones:
{
  "0": {
    "id": 2880,
    "x1": 4,
    "y1": 1442,
    "x2": 36,
    "y2": 1474
  },
  "1": {
    "id": 2944,
    "x1": 36,
    "y1": 1442,
    "x2": 68,
    "y2": 1474
  },
  "2": {
    "id": 3008,
    "x1": 68,
    "y1": 1442,
    "x2": 100,
    "y2": 1474
  },
  "3": {
    "id": 2881,
    "x1": 4,
    "y1": 1474,
    "x2": 36,
    "y2": 1506
  },
  "4": {
    "id": 2945,
    "x1": 36,
    "y1": 1474,
    "x2": 68,
    "y2": 1506
  },
  "5": {
    "id": 3009,
    "x1": 68,
    "y1": 1474,
    "x2": 100,
    "y2": 1506
  },
  "6": {
    "id": 2882,
    "x1": 4,
    "y1": 1506,
    "x2": 36,
    "y2": 1538
  },
  "7": {
    "id": 2946,
    "x1": 36,
    "y1": 1506,
    "x2": 68,
    "y2": 1538
  },
  "8": {
    "id": 3010,
    "x1": 68,
    "y1": 1506,
    "x2": 100,
    "y2": 1538
  }
}
2025-05-05 00:37:39 [debug]: [ZoneSearchService] Processing zone 2945
2025-05-05 00:37:39 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
2025-05-05 00:37:39 [debug]: [ZoneNetwork] Entered zones: 2880, 2944, 3008, 2881, 2945, 3009, 2882, 2946, 3010
2025-05-05 00:37:39 [debug]: [EjectService] Zones entered, waiting for field objects
2025-05-05 00:37:39 [debug]: [SearchStrategy] Incremented step to 1
2025-05-05 00:37:39 [debug]: [SocketMessageHandler] Field objects data contains no objects
2025-05-05 00:37:40 [debug]: [ZoneSearchService] Processing zone 3137
2025-05-05 00:37:40 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:40 [debug]: [ZoneNetwork] Exited zones: 2880, 2944, 3008, 2881, 2945, 3009, 2882, 2946, 3010
2025-05-05 00:37:40 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
2025-05-05 00:37:40 [debug]: [ZoneNetwork] Entered zones: 3072, 3136, 3200, 3073, 3137, 3201, 3074, 3138, 3202
2025-05-05 00:37:40 [debug]: [EjectService] Zones entered, waiting for field objects
2025-05-05 00:37:40 [debug]: [SearchStrategy] Incremented step to 2
2025-05-05 00:37:40 [debug]: [SocketMessageHandler] Sample kingdom objects:
{
  "0": {
    "id": "60f8f91878e4b11414d19b31",
    "loc": [
      11,
      19,
      1626
    ],
    "level": 18,
    "name": "MaxUA",
    "alliance": "wise"
  },
  "1": {
    "id": "64d9287221c23b5c89b70fd0",
    "loc": [
      11,
      71,
      1626
    ],
    "level": 6,
    "name": "Shrimpland",
    "alliance": "Earn"
  }
}
2025-05-05 00:37:40 [debug]: [SocketEvents] Dispatching field:objects event
2025-05-05 00:37:40 [debug]: [EjectService] Processing 2 new kingdoms for continent 11
2025-05-05 00:37:40 [debug]: API Response Details:
{
  "method": "POST",
  "url": "https://api-lok-live.leagueofkingdoms.com/api/field/cvc/king/kick",
  "data": {
    "result": true,
    "fo": {
      "occupied": {
        "id": "60f8f91878e4b11414d19b2a",
        "started": "2021-07-22T04:50:32.691Z"
      },
      "_id": "60f8f91878e4b11414d19b31",
      "loc": [
        11,
        19,
        1626
      ],
      "originLoc": [],
      "dummies": [
        "60f8f91878e4b11414d19b32",
        "60f8f91878e4b11414d19b33",
        "60f8f91878e4b11414d19b34"
      ],
      "code": 20300101,
      "level": 18,
      "created": "2021-07-22T04:50:32.691Z",
      "state": 1,
      "__v": 0
    }
  },
  "isEncrypted": false
}
2025-05-05 00:37:40 [info]: [EjectService] Successfully ejected kingdom 60f8f91878e4b11414d19b31
2025-05-05 00:37:40 [info]: [EjectService] Kicked player from CVC: MaxUA (Location: undefined,undefined)
2025-05-05 00:37:41 [debug]: [ZoneSearchService] Processing zone 3140
2025-05-05 00:37:41 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:41 [debug]: [ZoneNetwork] Exited zones: 3072, 3136, 3200, 3073, 3137, 3201, 3074, 3138, 3202
2025-05-05 00:37:41 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
2025-05-05 00:37:41 [debug]: [ZoneNetwork] Entered zones: 3075, 3139, 3203, 3076, 3140, 3204, 3077, 3141, 3205
2025-05-05 00:37:41 [debug]: [EjectService] Zones entered, waiting for field objects
2025-05-05 00:37:41 [debug]: [SearchStrategy] Incremented step to 3
2025-05-05 00:37:41 [debug]: [SocketMessageHandler] Sample kingdom objects:
{
  "0": {
    "id": "656ff6d351cf361e2079da34",
    "loc": [
      11,
      128,
      1629
    ],
    "level": 25,
    "name": "filomena"
  },
  "1": {
    "id": "6468848e1fe9da72c0fd0808",
    "loc": [
      11,
      164,
      1552
    ],
    "level": 10,
    "name": "Xxxxxxxxxxx"
  }
}
2025-05-05 00:37:41 [debug]: [SocketEvents] Dispatching field:objects event
2025-05-05 00:37:41 [debug]: [EjectService] Processing 2 new kingdoms for continent 11
2025-05-05 00:37:41 [debug]: API Response Details:
{
  "method": "POST",
  "url": "https://api-lok-live.leagueofkingdoms.com/api/field/cvc/king/kick",
  "data": {
    "result": true,
    "fo": {
      "occupied": {
        "id": "61771585c01da608b8b9509e",
        "started": "2023-12-06T04:21:39.654Z"
      },
      "_id": "656ff6d351cf361e2079da34",
      "loc": [
        11,
        128,
        1629
      ],
      "originLoc": [],
      "dummies": [
        "656ff6d351cf361e2079da35",
        "656ff6d351cf361e2079da36",
        "656ff6d351cf361e2079da37"
      ],
      "code": 20300101,
      "level": 25,
      "created": "2023-12-06T04:21:39.654Z",
      "state": 1,
      "__v": 0
    }
  },
  "isEncrypted": false
}
2025-05-05 00:37:41 [debug]: API Response Details:
{
  "method": "POST",
  "url": "https://api-lok-live.leagueofkingdoms.com/api/field/cvc/king/kick",
  "data": {
    "result": true,
    "fo": {
      "occupied": {
        "id": "60fc3eaa04c85c50a4091709",
        "started": "2023-08-13T19:01:06.347Z"
      },
      "_id": "64d9287221c23b5c89b70fd0",
      "loc": [
        11,
        71,
        1626
      ],
      "originLoc": [],
      "dummies": [
        "64d9287221c23b5c89b70fd1",
        "64d9287221c23b5c89b70fd2",
        "64d9287221c23b5c89b70fd3"
      ],
      "code": 20300101,
      "level": 6,
      "created": "2023-08-13T19:01:06.347Z",
      "state": 1,
      "__v": 0
    }
  },
  "isEncrypted": false
}
2025-05-05 00:37:41 [info]: [EjectService] Successfully ejected kingdom 656ff6d351cf361e2079da34
2025-05-05 00:37:41 [info]: [EjectService] Kicked player from CVC: filomena (Location: undefined,undefined)
2025-05-05 00:37:41 [info]: [EjectService] Successfully ejected kingdom 64d9287221c23b5c89b70fd0
2025-05-05 00:37:41 [info]: [EjectService] Kicked player from CVC: Shrimpland (Location: undefined,undefined)
2025-05-05 00:37:41 [debug]: API Response Details:
{
  "method": "POST",
  "url": "https://api-lok-live.leagueofkingdoms.com/api/field/cvc/king/kick",
  "data": {
    "result": true,
    "fo": {
      "occupied": {
        "id": "6212fcef13a45745448ea41f",
        "started": "2023-05-20T08:27:58.828Z"
      },
      "_id": "6468848e1fe9da72c0fd0808",
      "loc": [
        11,
        164,
        1552
      ],
      "originLoc": [],
      "dummies": [
        "6468848e1fe9da72c0fd0809",
        "6468848e1fe9da72c0fd080a",
        "6468848e1fe9da72c0fd080b"
      ],
      "code": 20300101,
      "level": 10,
      "created": "2023-05-20T08:27:58.828Z",
      "state": 1,
      "__v": 0
    }
  },
  "isEncrypted": false
}
2025-05-05 00:37:42 [info]: [EjectService] Successfully ejected kingdom 6468848e1fe9da72c0fd0808
2025-05-05 00:37:42 [info]: [EjectService] Kicked player from CVC: Xxxxxxxxxxx (Location: undefined,undefined)
2025-05-05 00:37:42 [debug]: [ZoneSearchService] Processing zone 2948
2025-05-05 00:37:42 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:42 [debug]: [ZoneNetwork] Exited zones: 3075, 3139, 3203, 3076, 3140, 3204, 3077, 3141, 3205
2025-05-05 00:37:42 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
2025-05-05 00:37:42 [debug]: [ZoneNetwork] Entered zones: 2883, 2947, 3011, 2884, 2948, 3012, 2885, 2949, 3013
2025-05-05 00:37:42 [debug]: [EjectService] Zones entered, waiting for field objects
2025-05-05 00:37:42 [debug]: [SearchStrategy] Incremented step to 4
2025-05-05 00:37:43 [debug]: [ZoneSearchService] Processing zone 2756
2025-05-05 00:37:43 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:43 [debug]: [ZoneNetwork] Exited zones: 2883, 2947, 3011, 2884, 2948, 3012, 2885, 2949, 3013
2025-05-05 00:37:43 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
2025-05-05 00:37:43 [debug]: [ZoneNetwork] Entered zones: 2691, 2755, 2819, 2692, 2756, 2820, 2693, 2757, 2821
2025-05-05 00:37:43 [debug]: [EjectService] Zones entered, waiting for field objects
2025-05-05 00:37:43 [debug]: [SearchStrategy] Incremented step to 5
2025-05-05 00:37:44 [debug]: [ZoneSearchService] Processing zone 2753
2025-05-05 00:37:44 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:44 [debug]: [ZoneNetwork] Exited zones: 2691, 2755, 2819, 2692, 2756, 2820, 2693, 2757, 2821
2025-05-05 00:37:44 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
2025-05-05 00:37:44 [debug]: [ZoneNetwork] Entered zones: 2688, 2752, 2816, 2689, 2753, 2817, 2690, 2754, 2818
2025-05-05 00:37:44 [debug]: [EjectService] Zones entered, waiting for field objects
2025-05-05 00:37:44 [debug]: [SearchStrategy] Incremented step to 6
2025-05-05 00:37:44 [debug]: [SocketMessageHandler] Sample kingdom objects:
{
  "0": {
    "id": "65f64df04266f1afbd023a5a",
    "loc": [
      11,
      49,
      1370
    ],
    "level": 9,
    "name": "osmar007007"
  }
}
2025-05-05 00:37:44 [debug]: [SocketEvents] Dispatching field:objects event
2025-05-05 00:37:44 [debug]: [EjectService] Processing 1 new kingdoms for continent 11
2025-05-05 00:37:44 [debug]: API Response Details:
{
  "method": "POST",
  "url": "https://api-lok-live.leagueofkingdoms.com/api/field/cvc/king/kick",
  "data": {
    "result": true,
    "fo": {
      "occupied": {
        "id": "60f86b9328c6755a1d8503b1",
        "started": "2024-03-17T01:57:04.182Z"
      },
      "_id": "65f64df04266f1afbd023a5a",
      "loc": [
        11,
        49,
        1370
      ],
      "originLoc": [],
      "dummies": [
        "65f64df04266f1afbd023a5b",
        "65f64df04266f1afbd023a5c",
        "65f64df04266f1afbd023a5d"
      ],
      "code": 20300101,
      "level": 9,
      "created": "2024-03-17T01:57:04.182Z",
      "state": 1,
      "__v": 0
    }
  },
  "isEncrypted": false
}
2025-05-05 00:37:44 [info]: [EjectService] Successfully ejected kingdom 65f64df04266f1afbd023a5a
2025-05-05 00:37:44 [info]: [EjectService] Kicked player from CVC: osmar007007 (Location: undefined,undefined)
2025-05-05 00:37:45 [debug]: [ZoneSearchService] Processing zone 2750
2025-05-05 00:37:45 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:45 [debug]: [ZoneNetwork] Exited zones: 2688, 2752, 2816, 2689, 2753, 2817, 2690, 2754, 2818
2025-05-05 00:37:45 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
2025-05-05 00:37:45 [debug]: [ZoneNetwork] Entered zones: 2685, 2749, 2813, 2686, 2750, 2814, 2687, 2751, 2815
2025-05-05 00:37:45 [debug]: [EjectService] Zones entered, waiting for field objects
2025-05-05 00:37:45 [debug]: [SearchStrategy] Incremented step to 7
2025-05-05 00:37:46 [debug]: [ZoneSearchService] Processing zone 2942
2025-05-05 00:37:46 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:46 [debug]: [ZoneNetwork] Exited zones: 2685, 2749, 2813, 2686, 2750, 2814, 2687, 2751, 2815
2025-05-05 00:37:46 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
2025-05-05 00:37:46 [debug]: [ZoneNetwork] Entered zones: 2877, 2941, 3005, 2878, 2942, 3006, 2879, 2943, 3007
2025-05-05 00:37:46 [debug]: [EjectService] Zones entered, waiting for field objects
2025-05-05 00:37:46 [debug]: [SearchStrategy] Incremented step to 8
2025-05-05 00:37:46 [info]: [ZoneSearchService] Taking a break, reconnecting socket
2025-05-05 00:37:46 [info]: [ZoneSearchService] Stopped for continent 11
2025-05-05 00:37:46 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:46 [debug]: [ZoneNetwork] Exited zones: 2877, 2941, 3005, 2878, 2942, 3006, 2879, 2943, 3007
2025-05-05 00:37:46 [info]: [EjectService] Reconnecting socket...
2025-05-05 00:37:46 [debug]: [SocketService] socc state changed to closing
2025-05-05 00:37:46 [debug]: [SocketService] socc state changed to closed
2025-05-05 00:37:46 [debug]: [SocketService] sock state changed to closing
2025-05-05 00:37:46 [debug]: [SocketService] sock state changed to closed
2025-05-05 00:37:46 [debug]: [SocketService] Sending to socf:/field/leave
2025-05-05 00:37:46 [debug]: [SocketService] socf state changed to closing
2025-05-05 00:37:46 [debug]: [SocketService] socf state changed to closed
2025-05-05 00:37:46 [info]: [WsInstance] Closed socket service for Continent 11
2025-05-05 00:37:46 [info]: [KingdomService] Entering kingdom for Continent 11
2025-05-05 00:37:46 [warn]: [SocketConnection] sock WebSocket closed: 1005 - 
2025-05-05 00:37:46 [debug]: [SocketService] sock state changed to disconnected
2025-05-05 00:37:46 [warn]: [SocketConnection] socf WebSocket closed: 1005 - 
2025-05-05 00:37:46 [debug]: [SocketService] socf state changed to disconnected
2025-05-05 00:37:46 [warn]: [SocketConnection] socc WebSocket closed: 1005 - 
2025-05-05 00:37:46 [debug]: [SocketService] socc state changed to disconnected
2025-05-05 00:37:46 [debug]: API Response Details:
{
  "method": "POST",
  "url": "https://api-lok-live.leagueofkingdoms.com/api/kingdom/enter",
  "data": {
    "result": true,
    "kingdom": {
      "wall": {
        "maxDurability": 15000,
        "durability": 16000,
        "fireType": 0,
        "lastRepairDate": "2024-04-28T08:55:22.496Z"
      },
      "bf": {
        "worldId": 100002,
        "entered": "2025-05-05T00:06:50.538Z",
        "status": 1
      },
      "boost": {
        "total": [
          [
            11,
            4,
            4
          ],
          [
            12,
            1,
            125000
          ],
          [
            13,
            1,
            1200000
          ],
          [
            21,
            1,
            2100000
          ],
          [
            22,
            1,
            2100000
          ],
          [
            23,
            1,
            2100000
          ],
          [
            24,
            1,
            2100000
          ],
          [
     ...
2025-05-05 00:37:46 [info]: [KingdomService] Successfully entered kingdom C11 Rebound (66157e2cb5a1efcad045fcdd)
2025-05-05 00:37:46 [debug]: [KingdomService] Kingdom data:
{
  "networks": {
    "api": "https://api-lok-live.leagueofkingdoms.com/api",
    "kingdoms": [
      "https://sock-lok-live.leagueofkingdoms.com/socket.io/"
    ],
    "fields": [
      "https://socf-lok-live.leagueofkingdoms.com/socket.io/"
    ],
    "chats": [
      "https://socc-lok-live.leagueofkingdoms.com/socket.io/"
    ]
  },
  "kingdom": {
    "id": "66157e2cb5a1efcad045fcdd",
    "name": "C11 Rebound",
    "loc": [
      100002,
      36,
      1474
    ]
  }
}
2025-05-05 00:37:46 [info]: [WsInstance] Initializing socket service for Continent 11
2025-05-05 00:37:46 [debug]: [SocketService] socf state changed to connecting
2025-05-05 00:37:46 [debug]: [SocketService] socf state changed to connected
2025-05-05 00:37:46 [info]: [SocketConnection] Connected to socf WebSocket
2025-05-05 00:37:46 [debug]: [SocketService] socf state changed to initialized
2025-05-05 00:37:46 [debug]: [SocketService] Sending to socf:/field/enter/v3
2025-05-05 00:37:46 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:46 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
2025-05-05 00:37:46 [debug]: [EjectService] Subscribed to field objects events
2025-05-05 00:37:46 [debug]: [SearchStrategy] Initialized with 9 spiral steps
2025-05-05 00:37:46 [info]: [ZoneSearchService] Initialized for continent 11
2025-05-05 00:37:46 [info]: [EjectService] Zone search service initialized
2025-05-05 00:37:46 [info]: [EjectService] Socket reconnected successfully
2025-05-05 00:37:46 [info]: [ZoneSearchService] Started for continent 11
2025-05-05 00:37:46 [debug]: [SocketMessageHandler] Sample kingdom objects:
{
  "0": {
    "id": "6818011ab18807f81d3b491a",
    "loc": [
      100002,
      36,
      1474
    ],
    "level": 22,
    "name": "C11 Rebound",
    "alliance": "LWA2"
  }
}
2025-05-05 00:37:46 [debug]: [SocketEvents] Dispatching field:objects event
2025-05-05 00:37:46 [debug]: [EjectService] Processing 1 new kingdoms for continent 11
2025-05-05 00:37:46 [error]: [EjectService] Failed to eject kingdom 6818011ab18807f81d3b491a:
{
  "code": "same_kingdom",
  "retryable": false,
  "retryDelay": 0,
  "name": "BotError"
}
2025-05-05 00:37:47 [debug]: [SocketService] Sending to socf:/zone/leave/list/v2
2025-05-05 00:37:47 [debug]: [SocketService] Sending to socf:/zone/enter/list/v4
2025-05-05 00:37:47 [debug]: [SocketMessageHandler] Sample kingdom objects:
{
  "0": {
    "id": "6818011ab18807f81d3b491a",
    "loc": [
      100002,
      36,
      1474
    ],
    "level": 22,
    "name": "C11 Rebound",
    "alliance": "LWA2"
  }
}
2025-05-05 00:37:47 [debug]: [SocketEvents] Dispatching field:objects event