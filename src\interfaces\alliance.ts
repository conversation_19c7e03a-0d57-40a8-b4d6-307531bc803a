export interface AllianceInfo {
  members: AllianceMember[]
  isOpen?: boolean
}

export interface AllianceMember {
  kingdomId?: string
  name: string
  power?: string
  rank?: number
  faceCode?: number
  lastLogined: Date
  logined: boolean
  id?: string
  role?: string
}

export interface AllianceMemberGroup {
  _id: number
  members: AllianceMember[]
}

export interface AllianceMembersResponse {
  result: boolean
  members: AllianceMemberGroup[]
}

export interface AllianceData {
  id: string
  name: string
  power: number
  kills: number
  members: number
  leader: string
  isOpen: boolean
  territory: number
  ranking: number
}

export interface RankGroups {
  rank4: number
  rank3: number
  rank2: number
  rank1: number
}

export interface ApplicationListResponse {
  result: boolean
  requestList: Array<{
    _id: string
    name: string
    power: string
    faceCode: number
    kill: number
  }>
}

export interface Applicant {
  _id: string
  name: string
  power: string | number
}

export type ApplicationList = Applicant[]
