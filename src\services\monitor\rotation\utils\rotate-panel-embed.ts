import {
 EmbedBuilder, type Client, type TextChannel 
} from 'discord.js'

import type { InstanceConfig } from '../../../../interfaces/config.js'
import { fetchTotalMembers } from '../../../alliance/rotate-service.js'
import { logger } from '../../../../utils/logging/logger.js'

export async function createRotatePanel(instanceConfig: InstanceConfig) {
  try {
    const totalMembers = await fetchTotalMembers(instanceConfig)
    const rotateConfig = instanceConfig.rotate

    const embed = new EmbedBuilder()
      .setColor(rotateConfig?.enabled ? '#00FF00' : '#FF0000')
      .setTitle('Alliance Rotation Panel')
      .setDescription('Use the buttons below to manage alliance rotation.')
      .addFields(
        {
          name: 'Status',
          value: rotateConfig?.enabled ? 'Active' : 'Inactive',
          inline: true
        },
        {
          name: 'Alliance',
          value: rotateConfig?.isOpen ? 'Open' : 'Closed',
          inline: true
        },
        { name: 'Total Members', value: totalMembers.toString(), inline: true },
        {
          name: 'Kick Threshold',
          value: `${rotateConfig?.kickThreshold} hours`,
          inline: true
        },
        {
          name: 'Max Kick',
          value: rotateConfig?.maxKick.toString() || 'Not set',
          inline: true
        }
      )
      .setTimestamp()

    return { embeds: [embed] }
  } catch (error) {
    logger.error('Error creating rotate panel embed:', error)
    return {
      embeds: [
        new EmbedBuilder()
          .setColor('#FF0000')
          .setTitle('Error')
          .setDescription(
            'Failed to create rotate panel. Please try again later.'
          )
      ]
    }
  }
}

export async function updateRotatePanel(
  client: Client,
  instanceConfig: InstanceConfig
) {
  if (!instanceConfig.rotate?.channelId || !instanceConfig.rotate?.messageId) {
    return
  }

  try {
    const channel = (await client.channels.fetch(
      instanceConfig.rotate.channelId
    )) as TextChannel
    if (!channel || !channel.isTextBased()) {
      logger.error('Invalid rotate panel channel configuration.')
      return
    }

    const message = await channel.messages.fetch(
      instanceConfig.rotate.messageId
    )
    const updatedPanel = await createRotatePanel(instanceConfig)
    await message.edit(updatedPanel)
  } catch (error) {
    logger.error('Error updating rotate panel:', error)
  }
}
