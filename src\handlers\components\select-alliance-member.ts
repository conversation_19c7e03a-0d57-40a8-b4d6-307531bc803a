import {
  StringSelectMenuInteraction,
  StringSelectMenuBuilder,
  ActionRowBuilder
} from 'discord.js'
import { InstanceConfig } from 'interfaces/config.js'
import { fetchAllianceMembersRank } from 'services/alliance/rank-service.js'

export async function handleAllianceRankMemberSelection(
  interaction: StringSelectMenuInteraction,
  instanceConfig: InstanceConfig
): Promise<void> {
  const [kingdomId, currentRank] = interaction.values[0].split('|')
  const rankedMembers = await fetchAllianceMembersRank(instanceConfig)
  const allMembers = Object.values(rankedMembers).flat()
  const member = allMembers.find(m => m.kingdomId === kingdomId)

  if (!member) {
    await interaction.reply({
      content: 'Selected member not found.',
      ephemeral: true
    })
    return
  }

  const rankOptions = [1, 2, 3, 4].filter(rank => rank !== Number(currentRank))

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId(`select-rank-${kingdomId}`)
    .setPlaceholder('Select new rank')
    .addOptions(
      rankOptions.map(rank => ({
        label: `Rank ${rank}`,
        value: `${rank}`
      }))
    )

  const actionRow =
    new ActionRowBuilder<StringSelectMenuBuilder>().addComponents(selectMenu)

  await interaction.update({
    content: `Select a new rank for ${member.name}:`,
    components: [actionRow]
  })
}
