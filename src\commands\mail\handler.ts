import type { ChatInputCommandInteraction } from 'discord.js'
import { sendMailToAlliance } from 'services/alliance/mail-service.js'
import { ApiService } from 'services/api/api-service.js'
import {
 TitleToggleMap, RoleStatusMap 
} from 'services/title/types.js'

import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import type { InstanceConfig } from '../../interfaces/config.js'

export class HandleMail extends BaseCommandHandler {
  constructor(
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap: TitleToggleMap,
    roleStatusMap: RoleStatusMap
  ) {
    super(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap
    )
  }

  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return
    await this.ensureToken()
    await this.deferReply(false)

    const subject = this.interaction.options.getString('subject', true)
    const message = this.interaction.options.getString('message', true)

    try {
      await this.ensureToken()
      const result = await sendMailToAlliance(
        this.instanceConfig,
        subject,
        message
      )

      if (result.success) {
        if (this.instanceConfig.mail?.alertChannelId) {
          await this.sendDiscordNotification(
            this.instanceConfig.mail.alertChannelId,
            `📨 New Alliance Mail Sent 📨 \n📌 Subject: ${subject}\n💬 Message: ${message}`
          )
        }
        await this.interaction.followUp(
          'Mail sent successfully to alliance members.'
        )
      } else {
        await this.interaction.followUp(
          'Failed to send mail to alliance members.'
        )
      }
    } catch (error) {
      await this.handleError(
        error,
        'An error occurred while sending mail to alliance members.'
      )
    }
  }
}

export default {
  data: {
    name: 'mail',
    description: 'Send mail to alliance members',
    options: [
      {
        name: 'subject',
        description: 'The subject of the mail',
        type: 3,
        required: true
      },
      {
        name: 'message',
        description: 'The message content',
        type: 3,
        required: true
      }
    ]
  },
  CommandHandlerClass: HandleMail
}
