export interface AllianceData {
  id: string
  name: string
  power: number
  kills: number
  members: number
  leader: string
  isOpen: boolean
  territory: number
  ranking: number
}

export interface PlayerData {
  id: string
  name: string
  power: number
  kills: number
  alliance: string | null
  coordinates: {
    x: number
    y: number
  }
  lastActive: number
  vipLevel: number
  castleLevel: number
}

export interface ShrineData {
  nextTime: number
  isActive: boolean
  participants: string[]
  result: boolean
  data: {
    nextTime: number
    isActive: boolean
    participants: string[]
  }
}

export interface LandData {
  id: string
  contributions: {
    name: string
    total: number
    timestamp: number
  }[]
}

export interface RotateData {
  isActive: boolean
  lastRotation: number
  nextRotation: number
  members: {
    id: string
    name: string
    power: number
    kills: number
    role: string
    lastActive: number
  }[]
}
