import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import { fetchPlayerProfile } from '../../services/alliance/alliance-service.js'
import {
  addOrUpdatePlayerProfile,
  getAllPlayerProfiles,
  cleanUserDataByContinent
} from '../../services/storage/user-data-storage.js'
import { logger } from '../../utils/logging/logger.js'

export class HandlePlayerData extends BaseCommandHandler {
  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return
    await this.ensureToken()
    await this.deferReply(false)

    try {
      await cleanUserDataByContinent(this.instanceConfig)
      const updateSuccessful = await this.fetchAndUpdatePlayerData()

      if (!updateSuccessful) {
        await this.interaction.followUp({
          content: 'Player data failed to update.'
        })
        return
      }
      await this.interaction.editReply({
        content: 'Player data has been updated successfully!'
      })
    } catch (error) {
      logger.error(
        '[HandlePlayerData] Failed to update player data:',
        error instanceof Error ? error.message : String(error)
      )
      await this.handleError(
        error,
        'An error occurred while updating player data. Please check the logs for more information.'
      )
    }
  }

  async fetchAndUpdatePlayerData(): Promise<boolean> {
    const existingData = await getAllPlayerProfiles(this.instanceConfig)
    let updatedCount = 0

    for (const [discordId, profiles] of Object.entries(existingData)) {
      for (const profile of profiles) {
        try {
          const updatedProfile = await fetchPlayerProfile(
            this.instanceConfig,
            profile.id
          )
          if (updatedProfile) {
            await addOrUpdatePlayerProfile(
              this.instanceConfig,
              discordId,
              updatedProfile
            )
            updatedCount++
          }
        } catch (error) {
          logger.error(
            `[HandlePlayerData] Failed to fetch profile for Kingdom ID: ${profile.id}`,
            error instanceof Error ? error.message : String(error)
          )
        }
      }
    }

    logger.info(`[HandlePlayerData] Updated ${updatedCount} profiles.`)
    return updatedCount > 0
  }
}

export default {
  data: {
    name: 'player_data',
    description: 'Update player data for all stored profiles'
  },
  CommandHandlerClass: HandlePlayerData
}
