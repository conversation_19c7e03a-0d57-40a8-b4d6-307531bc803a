export class <PERSON>t<PERSON>rror extends <PERSON>rror {
  constructor(
    message: string,
    public code: string,
    public status?: number,
    public retryable = false,
    public retryDelay = 0
  ) {
    super(message)
    this.name = 'BotError'
  }
}

export class RetryableException extends Bot<PERSON>rror {
  constructor(
    message: string,
    code: string,
    retryDelay = 2000,
    name = 'RetryableException'
  ) {
    super(message, code, undefined, true, retryDelay)
    this.name = name
  }
}

export class FatalException extends BotError {
  constructor(message: string, code: string, name = 'FatalException') {
    super(message, code, undefined, false)
    this.name = name
  }
}

export class RetryableApiException extends RetryableException {
  constructor(message: string, code: string, retryDelay: number) {
    super(message, code, retryDelay, 'RetryableApiException')
  }
}

export class FatalApiException extends FatalException {
  constructor(message: string, code: string) {
    super(message, code, 'FatalApiException')
  }
}

export const ErrorTypes = {
  API: {
    RETRY: 'RetryableApiException',
    FATAL: 'FatalApiException'
  },
  SOCKET: {
    RETRY: 'RetryableSocketException',
    FATAL: 'FatalSocketException'
  }
}

export function createRetryableSocketError(
  message: string,
  code: string,
  retryDelay = 2000
): RetryableException {
  return new RetryableException(
    message,
    code,
    retryDelay,
    ErrorTypes.SOCKET.RETRY
  )
}

export function createFatalSocketError(
  message: string,
  code: string
): FatalException {
  return new FatalException(message, code, ErrorTypes.SOCKET.FATAL)
}
