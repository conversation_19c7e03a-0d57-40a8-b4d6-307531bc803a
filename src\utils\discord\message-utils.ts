import {
  type Message,
  type MessageCreateOptions,
  type MessageEditOptions,
  type MessagePayload,
  type TextChannel,
  EmbedBuilder,
  ActionRowBuilder,
  ButtonBuilder
} from 'discord.js'

import { logger } from '../logging/logger.js'

export type MessageContent = string | MessagePayload | MessageCreateOptions

export class MessageUtils {
  static async updateOrSendMessage(
    channel: TextChannel,
    content: MessageContent,
    messageId?: string | null
  ): Promise<Message | null> {
    try {
      if (messageId) {
        try {
          const message = await channel.messages.fetch(messageId)
          return await message.edit(
            content as string | MessagePayload | MessageEditOptions
          )
        } catch (error) {
          logger.warn(
            `Failed to update message ${messageId}, sending new message:`,
            error
          )
        }
      }
      return await channel.send(content)
    } catch (error) {
      logger.error('Error in updateOrSendMessage:', error)
      return null
    }
  }

  static async deleteMessage(
    channel: TextChannel,
    messageId: string
  ): Promise<boolean> {
    try {
      const message = await channel.messages.fetch(messageId)
      await message.delete()
      return true
    } catch (error) {
      logger.error(`Failed to delete message ${messageId}:`, error)
      return false
    }
  }

  static createEmbed(
    title: string,
    description: string,
    fields?: { name: string; value: string; inline?: boolean }[]
  ): EmbedBuilder {
    const embed = new EmbedBuilder()
      .setTitle(title)
      .setDescription(description)
      .setColor('#0099ff')
      .setTimestamp()

    if (fields) {
      embed.addFields(fields)
    }

    return embed
  }

  static createActionRow(
    ...buttons: ButtonBuilder[]
  ): ActionRowBuilder<ButtonBuilder> {
    return new ActionRowBuilder<ButtonBuilder>().addComponents(buttons)
  }

  static createButton(
    customId: string,
    label: string,
    style: number
  ): ButtonBuilder {
    return new ButtonBuilder()
      .setCustomId(customId)
      .setLabel(label)
      .setStyle(style)
  }

  static async sendTemporaryMessage(
    channel: TextChannel,
    content: MessageContent,
    duration: number
  ): Promise<void> {
    try {
      const message = await channel.send(content)
      setTimeout(() => {
        message.delete().catch(error => {
          logger.error('Failed to delete temporary message:', error)
        })
      }, duration)
    } catch (error) {
      logger.error('Failed to send temporary message:', error)
    }
  }
}

export async function bulkDelete(
  channel: TextChannel,
  messageIds: string[]
): Promise<boolean> {
  try {
    await channel.bulkDelete(messageIds)
    return true
  } catch (error) {
    logger.error(`Failed to bulk delete messages:`, error)
    return false
  }
}
