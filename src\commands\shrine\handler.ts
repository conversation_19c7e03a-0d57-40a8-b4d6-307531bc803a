import {
  type ChatInputCommandInteraction,
  EmbedBuilder,
  type TextChannel
} from 'discord.js'
import type { ApiService } from 'services/api/api-service.js'
import { shrineTypes } from 'services/shrine/types.js'
import type {
 TitleToggleMap, RoleStatusMap 
} from 'services/title/types.js'

import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import type { InstanceConfig } from '../../interfaces/config.js'
import {
  fetchShrineData,
  processShrine,
  createShrineEmbed
} from '../../services/shrine/shrine-service.js'
import type { ProcessedShrine } from '../../services/shrine/types.js'
import { setSettings } from '../../utils/config/config-utils.js'

class HandleShrine extends BaseCommandHandler {
  updateInterval: NodeJS.Timeout | null = null

  constructor(
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap: TitleToggleMap,
    roleStatusMap: RoleStatusMap
  ) {
    super(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap
    )
  }

  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return
    await this.ensureToken()
    await this.interaction.deferReply({ ephemeral: false })

    try {
      const shrineData = await fetchShrineData(this.instanceConfig)

      if (!Array.isArray(shrineData) || shrineData.length === 0) {
        throw new Error('Invalid or empty shrine data received')
      }

      const processedData = processShrine(shrineData)
      const embed = createShrineEmbed(processedData)

      if (this.instanceConfig.shrine && this.instanceConfig.shrine.channelId) {
        const channel = this.interaction.client.channels.cache.get(
          this.instanceConfig.shrine.channelId
        ) as TextChannel | undefined
        if (channel) {
          const message = await channel.send({ embeds: [embed] })
          setSettings(this.instanceConfig, 'shrine.shrineMessageId', message.id)
          await this.interaction.editReply(
            'Shrine timer has been posted in the designated channel.'
          )
        } else {
          await this.interaction.editReply(
            'Configured shrine channel not found.'
          )
        }
      } else {
        await this.interaction.editReply({ embeds: [embed] })
      }

      this.startUpdateInterval()
    } catch (error) {
      this.logger.error('Error in handleShrine:', error)
      await this.handleError(
        error instanceof Error ? error : new Error('Unknown error'),
        'An error occurred while processing shrine data. Please check the logs for more details.'
      )
    }
  }

  private startUpdateInterval(): void {
    if (this.updateInterval) {
      global.clearInterval(this.updateInterval)
    }
    this.updateInterval = global.setInterval(
      () => this.updateEmbed(),
      5 * 60 * 1000
    ) // 5 minutes
  }

  private async updateEmbed(): Promise<void> {
    try {
      const shrineData = await fetchShrineData(this.instanceConfig)

      if (!Array.isArray(shrineData) || shrineData.length === 0) {
        throw new Error('Invalid or empty shrine data received')
      }

      const processedData = processShrine(shrineData)
      const embed = createShrineEmbed(processedData)

      if (
        this.instanceConfig.shrine &&
        this.instanceConfig.shrine.shrineMessageId
      ) {
        const channel = this.interaction.client.channels.cache.get(
          this.instanceConfig.shrine.channelId
        ) as TextChannel | undefined
        if (channel) {
          try {
            const message = await channel.messages.fetch(
              this.instanceConfig.shrine.shrineMessageId
            )
            await message.edit({ embeds: [embed] })
          } catch (error) {
            this.logger.error(
              'Error fetching or editing shrine message:',
              error
            )
            const newMessage = await channel.send({ embeds: [embed] })
            setSettings(
              this.instanceConfig,
              'shrine.shrineMessageId',
              newMessage.id
            )
          }
        }
      }

      const upcomingGates = this.checkUpcomingGates(processedData)
      await this.sendGateAlerts(upcomingGates)
    } catch (error) {
      this.logger.error('Error updating shrine embed:', error)
    }
  }

  private checkUpcomingGates(
    processedData: ProcessedShrine[]
  ): ProcessedShrine[] {
    const oneHour = 60 // 60 minutes
    return processedData.filter(
      shrine =>
        shrine.type >= 14 &&
        shrine.type <= 18 &&
        shrine.timeRemaining <= oneHour
    )
  }

  private async sendGateAlerts(
    upcomingGates: ProcessedShrine[]
  ): Promise<void> {
    if (
      upcomingGates.length === 0 ||
      !this.instanceConfig.shrine?.alertChannelId
    )
      return

    const alertChannel = this.interaction.client.channels.cache.get(
      this.instanceConfig.shrine.alertChannelId
    ) as TextChannel | undefined
    if (!alertChannel) return

    const alertEmbed = new EmbedBuilder()
      .setColor('#FF9900')
      .setTitle('⚠️ CVC Gate Alerts ⚠️')
      .setDescription('The following gates will open within the next hour:')
      .addFields(
        upcomingGates.map(gate => ({
          name: `${shrineTypes[gate.type as keyof typeof shrineTypes].name} (${gate.coords.join(', ')})`,
          value: `Opening in: ${this.formatTime(gate.timeRemaining)}\nControlling Continent: ${gate.controllingContinent}`
        }))
      )
      .setTimestamp()

    await alertChannel.send({ embeds: [alertEmbed] })
  }

  private formatTime(minutes: number): string {
    const days = Math.floor(minutes / 1440)
    const hours = Math.floor((minutes % 1440) / 60)
    const mins = minutes % 60
    let result = ''
    if (days > 0) result += `${days}d `
    if (hours > 0 || days > 0) result += `${hours}h `
    result += `${mins}m`
    return result.trim()
  }
}

export default {
  data: {
    name: 'shrine_timer',
    description: 'Display shrine timers and set up alerts'
  },
  CommandHandlerClass: HandleShrine
}
