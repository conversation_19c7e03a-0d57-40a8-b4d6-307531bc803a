import { InstanceConfig } from '../../interfaces/config.js'

export function validateConfig(config: unknown): config is InstanceConfig {
  if (typeof config !== 'object' || config === null) {
    return false
  }

  const requiredFields: (keyof InstanceConfig)[] = [
    'username',
    'password',
    'guild_id',
    'enabledCommands',
    'adminRole',
    'continent',
    'allyID',
    'verify',
    'shrine'
  ]

  for (const field of requiredFields) {
    if (!(field in config)) {
      return false
    }
  }

  const typedConfig = config as InstanceConfig

  if (
    !Array.isArray(typedConfig.enabledCommands) ||
    !Array.isArray(typedConfig.allyID)
  ) {
    return false
  }

  if (typeof typedConfig.verify !== 'object' || typedConfig.verify === null) {
    return false
  }

  if (
    !('roleName' in typedConfig.verify) ||
    !('channelId' in typedConfig.verify)
  ) {
    return false
  }

  return true
}
