import { stringify } from 'querystring'

import axios, { type AxiosInstance } from 'axios'
import {
  BotError,
  FatalApiException,
  RetryableApiException
} from '../../utils/common/error.js'
import { withRetry } from '../../utils/common/retry-handler.js'
import { wait } from '../../utils/time/time-utils.js'

import {
 b64xorEnc, decodeRegionHash, parseData
} from '../../utils/decode.js'
import { logger } from '../../utils/logging/logger.js'
import { login } from '../auth/login-service.js'

import { updateParams } from './api-instance.js'
import type { Params } from './types.js'

const defaultHeaders = {
  accept: '*/*',
  'accept-language': 'en-US,en;q=0.9',
  'sec-ch-ua': '"Chromium";v="125", "Brave";v="125", "Not-A<PERSON>Brand";v="24"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-site',
  'sec-gpc': '1',
  referer: 'https://play.leagueofkingdoms.com/',
  origin: 'https://play.leagueofkingdoms.com',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Type': 'application/x-www-form-urlencoded'
}

const baseURL = 'https://api-lok-live.leagueofkingdoms.com/api'
const axiosInstance: AxiosInstance = axios.create({
  baseURL,
  headers: defaultHeaders,
  withCredentials: true
})

const defaultUrlConfig = {
  authenticated: true,
  encrypted: false
}

const encryptedUrls = [
  '/kingdom/world/change',
  '/kingdom/hospital/recover',
  '/kingdom/resource/harvest',
  '/kingdom/building/upgrade',
  '/kingdom/building/demolish',
  '/kingdom/treasure/exchange',
  '/kingdom/treasure/upgrade',
  '/kingdom/treasure/skill/upgrade',
  '/kingdom/task/speedup',
  '/kingdom/heal/speedup',
  '/kingdom/heal/instant',
  '/kingdom/vipshop/buy',
  '/kingdom/vip/claim',
  '/field/march/start',
  '/field/march/return',
  '/field/rally/start',
  '/field/rally/join',
  '/field/teleport',
  '/item/use',
  '/item/freechest',
  '/item/combine/skin',
  '/alliance/gift/claim',
  '/alliance/gift/claim/all',
  '/alliance/title',
  '/alliance/title/change',
  '/quest/claim',
  '/kingdom/profile/other',
  '/kingdom/profile/my',
  '/kingdom/task/all',
  '/field/march/info',
  '/alliance/gift/claim/all'
]

const urlConfig: Record<
  string,
  { authenticated: boolean; encrypted?: boolean }
> = {
  '/auth/login': {
    authenticated: false
  },
  '/stat/land/contribution': {
    authenticated: false
  }
}

const pendingRequests: Promise<unknown>[] = []

export type ApiService = ReturnType<typeof apiService>

const MAX_CONCURRENT_REQUESTS = 10

export class ApiError extends Error {
  constructor(
    message: string,
    public code: string,
    public status?: number
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

export default function apiService(params: Params) {
  let token = params.token ?? ''
  let xorKey = params.xorKey ?? ''
  let instanceConfig = params.instanceConfig

  async function request<T>(
    method: 'get' | 'post',
    url: string,
    data: unknown = {},
    headers: Record<string, string> = {}
  ): Promise<T> {
    const config = {
      ...defaultUrlConfig,
      ...(urlConfig[url] ?? {})
    }

    if (config.authenticated && !token) {
      const error = new BotError('No token available', 'no_auth')
      params.onError?.(error)
      throw error
    }

    if (config.authenticated) {
      headers = {
        ...headers,
        'x-access-token': token
      }
    }

    let requestData: string | undefined
    let requestParams = {}
    let isEncrypted = false

    if (method === 'post') {
      if (encryptedUrls.includes(url)) {
        const encodedPayload = b64xorEnc(xorKey, data)
        requestData = stringify({ json: encodedPayload })
        isEncrypted = true
      } else {
        requestData = stringify({ json: JSON.stringify(data) })
      }
    } else if (method === 'get') {
      if (encryptedUrls.includes(url)) {
        const encodedPayload = b64xorEnc(xorKey, data)
        requestParams = { json: encodedPayload }
        isEncrypted = true
      } else {
        requestParams = data
      }
    }

    const fullUrl = `${baseURL}${url}`

    try {
      const response: T = await withRetry(
        async () => {
          try {
            const axiosResponse = await (method === 'get'
              ? axiosInstance.get<T>(
                  url.includes('?')
                    ? url
                    : `${url}${Object.keys(requestParams).length ? '?' + stringify(requestParams) : ''}`,
                  { headers }
                )
              : axiosInstance.post<T>(url, requestData, { headers }))

            const parsedResponse = encryptedUrls.includes(url)
              ? parseData(axiosResponse.data, xorKey)
              : axiosResponse.data

            if (parsedResponse.err) {
              const errorCode = parsedResponse.err.code
              const errorMessage = parsedResponse.err.message

              switch (errorCode) {
                case 'duplicated':
                  throw new RetryableApiException(errorMessage, errorCode, 2000)
                case 'no_auth':
                  logger.warn(
                    'Received no_auth error, attempting to re-login...'
                  )
                  const loginResult = await login(instanceConfig)
                  if (!loginResult) {
                    throw new FatalApiException(
                      'Failed to re-login after no_auth error',
                      'LOGIN_FAILED'
                    )
                  }

                  token = loginResult.token
                  xorKey = decodeRegionHash(loginResult.regionHash)
                  updateParams(token, xorKey, instanceConfig)

                  return request<T>(method, url, data, {
                    ...headers,
                    'x-access-token': token
                  })
                case 'not_online':
                  throw new FatalApiException(errorMessage, errorCode)
                case 'exceed_limit_packet':
                  throw new RetryableApiException(
                    errorMessage,
                    errorCode,
                    3600000
                  )
                default:
                  throw new BotError(errorMessage, errorCode)
              }
            }

            return parsedResponse
          } catch (error) {
            if (axios.isAxiosError(error) && error.response?.status === 403) {
              logger.warn(
                'Received 403 error, waiting 5 minutes before retrying...'
              )
              await wait(10 * 60 * 1000) // Wait for 5 minutes

              const loginResult = await login(instanceConfig)
              if (!loginResult) {
                throw new FatalApiException(
                  'Failed to re-login after 403 error',
                  'LOGIN_FAILED'
                )
              }

              token = loginResult.token
              xorKey = decodeRegionHash(loginResult.regionHash)

              updateParams(token, xorKey, instanceConfig)

              return request<T>(method, url, data, headers)
            }
            throw error
          }
        },
        {
          shouldRetry: error => error instanceof RetryableApiException,
          onRetry: (error, attempt) => {
            logger.warn(`Retry attempt ${attempt} for ${url}:`, error)
          }
        }
      )

      logger.debug('API Response Details:', {
        method: method.toUpperCase(),
        url: fullUrl,
        data: response,
        isEncrypted
      })

      return response
    } catch (error) {
      if (
        error instanceof BotError ||
        error instanceof RetryableApiException ||
        error instanceof FatalApiException
      ) {
        throw error
      }

      if (axios.isAxiosError(error)) {
        logger.error(`API Error (${method.toUpperCase()} ${fullUrl}):`, {
          status: error.response?.status,
          data: error.response?.data,
          headers: error.response?.headers,
          message: error.message
        })
        throw new BotError(
          `API Error: ${error.message}`,
          'API_ERROR',
          error.response?.status
        )
      }

      logger.error(
        `Unexpected error in API request (${method.toUpperCase()} ${baseURL}${url}):`,
        error
      )
      throw new BotError('Unexpected API error', 'UNKNOWN_ERROR')
    }
  }

  function update(params: Params) {
    token = params.token ?? ''
    xorKey = params.xorKey ?? ''
    instanceConfig = params.instanceConfig
  }

  return {
    get: async <T>(url: string, params = {}): Promise<T> => {
      if (pendingRequests.length >= MAX_CONCURRENT_REQUESTS) {
        await Promise.all(pendingRequests)
        await wait(500)
      }
      const newRequest = request<T>('get', url, params)
      pendingRequests.push(newRequest)
      return newRequest.finally(async () => {
        await wait(50)
        const index = pendingRequests.indexOf(newRequest)
        if (index > -1) {
          pendingRequests.splice(index, 1)
        }
      })
    },
    post: async <T>(
      url: string,
      data: unknown = {},
      headers: Record<string, string> = {}
    ): Promise<T> => {
      if (pendingRequests.length > MAX_CONCURRENT_REQUESTS) {
        await Promise.all(pendingRequests)
        await wait(500)
      }
      const newRequest = request<T>('post', url, data, headers)
      pendingRequests.push(newRequest)
      return newRequest.finally(async () => {
        await wait(50)
        const index = pendingRequests.indexOf(newRequest)
        if (index > -1) {
          pendingRequests.splice(index, 1)
        }
      })
    },
    update
  }
}
