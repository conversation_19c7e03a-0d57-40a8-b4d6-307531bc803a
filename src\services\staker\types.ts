import type {
  Embed<PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  StringSelectMenuBuilder
} from 'discord.js'

export interface Staker {
  address: string
  stake: string
  rank: string
  comment?: string
}

export interface SelectRankOption {
  label: string
  value: string
  disabled?: boolean
}

export interface StakerEmbed {
  embed: EmbedBuilder
  components: ActionRowBuilder<StringSelectMenuBuilder>[]
}

export interface StakerMonitor {
  start(): void
  stop(): void
}

export interface StakerTier {
  min: number
  max: number
  icon: string
}

export const STAKER_TIERS: Record<number, StakerTier> = {
  1: { min: 0, max: 10000, icon: 'tier-1.png' },
  2: { min: 10000, max: 50000, icon: 'tier-2.png' },
  3: { min: 50000, max: 300000, icon: 'tier-3.png' },
  4: { min: 300000, max: 1000000, icon: 'tier-4.png' },
  5: { min: 1000000, max: Number.POSITIVE_INFINITY, icon: 'tier-5.png' }
}
