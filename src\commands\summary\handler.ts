import type {
  ChatInputCommandInteraction,
  EmbedBuilder,
  TextChannel
} from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'
import { getAllPlayerProfiles } from '../../services/storage/user-data-storage.js'
import { MessageUtils } from '../../utils/discord/message-utils.js'
import { logger } from '../../utils/logging/logger.js'

interface PlayerEntry {
  rank: number
  name: string
  points: number
}

export class SummaryHandler extends BaseCommandHandler {
  constructor(
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap: TitleToggleMap,
    roleStatusMap: RoleStatusMap
  ) {
    super(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap
    )
  }

  async execute(): Promise<void> {
    try {
      if (!(await this.validateRole(this.instanceConfig.adminRole))) return
      await this.ensureToken()
      await this.interaction.deferReply({ ephemeral: false })

      const allProfiles = await getAllPlayerProfiles(this.instanceConfig)

      const validData: PlayerEntry[] = Object.values(allProfiles)
        .flat()
        .map(profile => ({
          rank: profile.continentRank || 0,
          name: profile.name,
          points: profile.individualPoints || 0
        }))
        .filter(entry => entry.rank !== 0 && entry.name)
        .sort((a, b) => a.rank - b.rank)
        .slice(0, 200)

      if (validData.length === 0) {
        await this.interaction.editReply({
          content: 'No valid data available for the summary.'
        })
        return
      }

      const embeds: EmbedBuilder[] = []
      const chunkSize = 25
      for (let i = 0; i < validData.length; i += chunkSize) {
        const chunk = validData.slice(i, i + chunkSize)
        const embed = MessageUtils.createEmbed(
          `CONTINENT ${this.instanceConfig.continent} CVC TRACKER`,
          chunk
            .map(
              entry =>
                `${entry.rank} ${entry.name} ${entry.points.toLocaleString()}`
            )
            .join('\n')
        )

        if (i + chunkSize >= validData.length) {
          embed.setFooter({
            text: `Generated: ${new Date().toLocaleDateString('en-US')}`
          })
        }

        embeds.push(embed)
      }

      for (let i = 0; i < embeds.length; i += 10) {
        const embedBatch = embeds.slice(i, i + 10)
        await MessageUtils.updateOrSendMessage(
          this.interaction.channel as TextChannel,
          { embeds: embedBatch }
        )
      }

      await this.interaction.editReply({
        content: 'Summary generated successfully.'
      })

      logger.info(
        `Sent ${embeds.length} embeds containing ${validData.length} player entries.`
      )
    } catch (error) {
      logger.error(
        `Failed to generate summary: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
      await MessageUtils.sendTemporaryMessage(
        this.interaction.channel as TextChannel,
        'An error occurred while generating the summary. Please check the logs.',
        10000 // 10 seconds
      )
    }
  }
}

export default {
  data: {
    name: 'summary',
    description: 'Generate a summary of player data'
  },
  CommandHandlerClass: SummaryHandler
}
