import { Client } from 'discord.js'

import type { InstanceConfig } from '../../interfaces/config.js'
import type { KingdomObject } from '../../interfaces/object-types.js'
import { logger } from '../../utils/logging/logger.js'
import { getApiInstance } from '../api/api-instance.js'
import { updateEjectPanel } from '../monitor/eject/utils/eject-panel-embed.js'
import { socketEvents } from '../socket/utils/event-dispatcher.js'
import { SocketService } from 'services/socket/service.js'

export async function ejectKingdom(
  instanceConfig: InstanceConfig,
  kingdomId: string
): Promise<boolean> {
  // Always use instance continent for API calls
  const continent = instanceConfig.continent

  const api = getApiInstance(continent)

  try {
    await api.post(
      '/field/cvc/king/kick',
      {
        foId: kingdomId
      }
    )

    logger.info(`[EjectService] Successfully ejected kingdom ${kingdomId}`)
    return true
  } catch (error) {
    logger.error(
      `[EjectService] Failed to eject kingdom ${kingdomId}:`,
      error
    )
    return false
  }
}

export class EjectService {
  private isListening = false
  private processedKingdoms: Set<string> = new Set()
  private ejectedCount = 0
  private unsubscribeCallback: (() => void) | null = null
  private client: Client | null = null
  private reconnectTimer: NodeJS.Timeout | null = null

  constructor(
    private readonly instanceConfig: InstanceConfig,
  ) {}

  public setClient(client: Client): void {
    this.client = client
  }

  // This method is called by the button handler but we don't actually need to store the socket service
  // since we're using the event system. We'll just log the call.
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public setSocketService(_socketService: SocketService): void {
    logger.debug('[EjectService] Socket service provided (not stored)')
  }
  private subscribeToFieldObjects(): void {
    if (this.unsubscribeCallback) {
      this.unsubscribeCallback()
      this.unsubscribeCallback = null
    }

    this.unsubscribeCallback = socketEvents.on("field:object/update", (detail) => {
      // Log the exact data we receive with a distinctive marker for easier log searching
      logger.info(`[FIELD_OBJECT_UPDATE] EjectService received event: ${JSON.stringify(detail)}`)

      if (!this.isListening) {
        logger.debug(`[FIELD_OBJECT_UPDATE] EjectService not listening, ignoring event`)
        return
      }

      // Expected format: { objects: KingdomObject[] }
      const objects = (detail as { objects: KingdomObject[] }).objects || []

      if (!objects || objects.length === 0) {
        logger.warn(`[FIELD_OBJECT_UPDATE] EjectService received no objects in event or invalid format: ${JSON.stringify(detail)}`)
        return
      }

      logger.info(`[FIELD_OBJECT_UPDATE] EjectService processing ${objects.length} kingdom objects`)

      for (const kingdom of objects) {
        if (!kingdom) {
          logger.debug(`[FIELD_OBJECT_UPDATE] EjectService skipping null kingdom object`)
          continue
        }

        if (!kingdom.occupied) {
          logger.debug(`[FIELD_OBJECT_UPDATE] EjectService skipping unoccupied kingdom: ${kingdom._id}`)
          continue
        }

        if (this.processedKingdoms.has(kingdom._id)) {
          logger.debug(`[FIELD_OBJECT_UPDATE] EjectService skipping already processed kingdom: ${kingdom._id}`)
          continue
        }

        logger.info(`[FIELD_OBJECT_UPDATE] EjectService processing kingdom: ${kingdom._id} (${kingdom.occupied.name})`)

        this.processKingdom(kingdom).catch(error => {
          logger.error(`[FIELD_OBJECT_UPDATE] EjectService error processing kingdom ${kingdom?.occupied?.name}:`, error)
        })
      }
    })

    logger.debug('[EjectService] Subscribed to field objects events')
  }


  public startListening(): void {
    if (this.isListening) return

    this.processedKingdoms.clear()
    this.ejectedCount = 0
    this.subscribeToFieldObjects()

    this.isListening = true
    this.instanceConfig.eject.enabled = true

    // Get field continent for logging
    const fieldContinent = this.instanceConfig.kingdomData?.kingdom?.loc?.[0]

    logger.info(`[EjectService] Started for instance continent ${this.instanceConfig.continent}, field continent ${fieldContinent || 'unknown'}`)

    if (this.client) {
      updateEjectPanel(this.client, this.instanceConfig)
    }
  }

  public stopListening(): void {
    if (!this.isListening) return

    if (this.unsubscribeCallback) {
      this.unsubscribeCallback()
      this.unsubscribeCallback = null
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    this.isListening = false

    this.instanceConfig.eject.enabled = false

    const fieldContinent = this.instanceConfig.kingdomData?.kingdom?.loc?.[0]
    logger.info(`[EjectService] Stopped for field continent ${fieldContinent || 'unknown'}. Ejected ${this.ejectedCount} kingdoms.`)

    if (this.client) {
      updateEjectPanel(this.client, this.instanceConfig)
    }
  }



  private async processKingdom(kingdom: KingdomObject): Promise<void> {
    try {
      if (!kingdom || !kingdom.occupied) {
        logger.debug(`[FIELD_OBJECT_UPDATE] Cannot process kingdom: invalid kingdom object`)
        return
      }

      this.processedKingdoms.add(kingdom._id)

      const { _id, loc } = kingdom
      const { name } = kingdom.occupied

      logger.info(`[FIELD_OBJECT_UPDATE] Attempting to eject kingdom: ${_id} (${name}) at [${loc.continent},${loc.x},${loc.y}]`)

      const success = await ejectKingdom(this.instanceConfig, _id)

      if (success) {
        this.ejectedCount++

        logger.info(`[FIELD_OBJECT_UPDATE] Successfully ejected player from CVC: ${name} (Location: ${loc.x},${loc.y})`)

        if (this.client) {
          updateEjectPanel(this.client, this.instanceConfig)
        }
      } else {
        logger.warn(`[FIELD_OBJECT_UPDATE] Failed to eject player: ${name} (Location: ${loc.x},${loc.y})`)
      }
    } catch (error) {
      logger.error(`[FIELD_OBJECT_UPDATE] Error processing kingdom ${kingdom?.occupied?.name}:`, error)
    }
  }

  public getEjectedCount(): number {
    return this.ejectedCount;
  }


  public reset(): void {
    this.processedKingdoms.clear()
    this.ejectedCount = 0
    const fieldContinent = this.instanceConfig.kingdomData?.kingdom?.loc?.[0]
    logger.debug(`[EjectService] Reset for field continent ${fieldContinent || 'unknown'}`)
  }

  public isActive(): boolean {
    return this.isListening;
  }
}
