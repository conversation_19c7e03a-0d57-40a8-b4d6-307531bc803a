import path from 'path'

import * as ethers from 'ethers'
const { formatUnits } = ethers

import {
  type Client,
  type TextChan<PERSON>,
  EmbedBuilder,
  ActionRowBuilder,
  StringSelectMenuBuilder,
  AttachmentBuilder
} from 'discord.js'
import { logger } from 'utils/logging/logger'

import type { InstanceConfig } from '../../../interfaces/config'
import type { Staker } from '../types'
import {
  STAKER_TIERS,
  type SelectRankOption
} from '../types'
import { setSettings } from '../../../utils/config/config-utils.js'

export async function updateStakerPanelEmbed(
  client: Client,
  instanceConfig: InstanceConfig,
  stakers: Staker[],
  totalLOKAFormatted: string
): Promise<void> {
  try {
    logger.info('Starting updateStakerPanelEmbed')

    if (!instanceConfig.staker?.channelId) {
      logger.warn('No staker channel ID configured')
      return
    }

    const channel = (await client.channels.fetch(
      instanceConfig.staker.channelId
    )) as TextChannel
    if (!channel) {
      logger.error(
        `Staker channel not found: ${instanceConfig.staker.channelId}`
      )
      return
    }

    logger.info('Creating staker embed')
    const { embed, components, attachment } = createStakerEmbed(
      stakers,
      totalLOKAFormatted,
      instanceConfig.continent
    )

    let message
    if (instanceConfig.staker.messageId) {
      try {
        logger.info(
          `Fetching existing message: ${instanceConfig.staker.messageId}`
        )
        message = await channel.messages.fetch(instanceConfig.staker.messageId)
        logger.info('Updating existing message')
        await message.edit({ embeds: [embed], components, files: [attachment] })
      } catch (error) {
        logger.error('Error fetching or updating existing message:', error)
        message = null
      }
    }

    if (!message) {
      logger.info('Sending new message')
      message = await channel.send({
        embeds: [embed],
        components,
        files: [attachment]
      })
      setSettings(instanceConfig, 'staker.messageId', message.id)
      logger.info(`New message sent with ID: ${message.id}`)
    }

    logger.info('Staker panel embed updated successfully')
  } catch (error) {
    logger.error('Error in updateStakerPanelEmbed:', error)
    throw new Error('Failed to update staker panel embed')
  }
}

export function createStakerEmbed(
  stakers: Staker[],
  totalLOKAFormatted: string,
  continent: number
): {
  embed: EmbedBuilder
  components: ActionRowBuilder<StringSelectMenuBuilder>[]
  attachment: AttachmentBuilder
} {
  const title = `__**CONTINENT ${continent} PLEDGING**__`
  const description = stakers
    .map(staker => {
      const formattedStake = formatUnits(staker.stake, 18)
      return `**${staker.rank}**${staker.comment ? `, ${staker.comment}` : ''} __(${formattedStake} LOKA)__`
    })
    .join('\n')

  const totalLOKA = Number.parseFloat(totalLOKAFormatted)
  const tier =
    Object.values(STAKER_TIERS).find(
      t => totalLOKA >= t.min && totalLOKA < t.max
    ) || STAKER_TIERS[1]
  const attachment = new AttachmentBuilder(
    path.join(process.cwd(), 'icons', tier.icon)
  )

  const embed = new EmbedBuilder()
    .setTitle(title)
    .setDescription(description || 'No staker information available.')
    .setColor('#0099FF')
    .setFooter({
      text: `TOTAL PLEDGED = ${totalLOKAFormatted} LOKA`,
      iconURL: `attachment://${tier.icon}`
    })

  const unverifiedStakers = stakers.filter(staker => !staker.comment)

  const selectOptions: SelectRankOption[] =
    unverifiedStakers.length > 0
      ? unverifiedStakers.map(staker => ({
          label: staker.rank,
          value: staker.address
        }))
      : [
          {
            label: 'All Ranks have been Verified',
            value: 'all-verified',
            disabled: true
          }
        ]

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId('staker-comments')
    .setPlaceholder('Verify your Rank')
    .addOptions(selectOptions)

  const row = new ActionRowBuilder<StringSelectMenuBuilder>().addComponents(
    selectMenu
  )

  return { embed, components: [row], attachment }
}
