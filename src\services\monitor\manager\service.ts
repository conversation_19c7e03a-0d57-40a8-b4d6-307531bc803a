import type { Client } from 'discord.js'
import type {
 ApplicationListResponse, Applicant 
} from 'interfaces/alliance'
import type { InstanceConfig } from 'interfaces/config'
import { sendMail } from 'services/alliance/mail-service'
import { BotError } from 'utils/common/error.js'
import { updateManagerPanel } from 'services/monitor/manager/utils/manager-panel-embed'
import { logger } from 'utils/logging/logger'

import { getApiInstance } from '../../api/api-instance'

export class AllianceManagerMonitor {
  private intervalId: NodeJS.Timeout | null = null
  private readonly CHECK_INTERVAL = 60000 // 1 minute

  constructor(
    private readonly client: Client,
    private readonly instanceConfig: InstanceConfig
  ) {}

  async start(): Promise<void> {
    if (this.intervalId) {
      return
    }

    this.intervalId = setInterval(async () => {
      try {
        await this.reviewApplications()
      } catch (error) {
        logger.error('Error in alliance manager monitor:', error)
      }
    }, this.CHECK_INTERVAL)

    logger.info(
      `Started alliance manager monitor for continent ${this.instanceConfig.continent}`
    )
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
      logger.info(
        `Stopped alliance manager monitor for continent ${this.instanceConfig.continent}`
      )
    }
  }

  private async reviewApplications(): Promise<void> {
    if (!this.instanceConfig.manage?.enabled) {
      logger.debug(`Alliance manager is disabled for continent ${this.instanceConfig.continent}, skipping review`);
      return;
    }
    const api = getApiInstance(this.instanceConfig.continent)

    try {
      const response = await api.post<ApplicationListResponse>(
        '/alliance/request/list',
        {}
      )

      if (!response.result || !Array.isArray(response.requestList)) {
        throw new BotError(
          'Invalid API response structure',
          'INVALID_API_RESPONSE'
        )
      }

      for (const applicant of response.requestList) {
        await this.processApplicant(applicant)
      }

      await updateManagerPanel(this.client, this.instanceConfig)
    } catch (error) {
      if (error instanceof BotError) {
        logger.error(`BotError in reviewApplications: ${error.message}`)
      } else {
        logger.error('Error processing alliance applications:', error)
      }
    }
  }

  private async processApplicant(applicant: Applicant): Promise<void> {
    const { _id, name, power } = applicant
    const minPower = Number(this.instanceConfig.manage.minPower)
    const api = getApiInstance(this.instanceConfig.continent)

    if (Number(power) >= minPower) {
      await api.post('/alliance/request/accept', { kingdomId: _id })
      logger.info(`Accepted application from ${name} (Power: ${power})`)
    } else {
      await api.post('/alliance/request/deny', { kingdomId: _id })
      await sendMail(this.instanceConfig, name, _id)
      logger.info(`Denied application from ${name} (Power: ${power})`)
    }
  }
}
