/* eslint-disable @typescript-eslint/no-explicit-any */
class Cache<T> {
  private cache: Map<string, { data: T; timestamp: number }>
  private ttl: number

  constructor(ttlMinutes = 5) {
    this.cache = new Map()
    this.ttl = ttlMinutes * 60 * 1000
  }

  set(key: string, value: T): void {
    this.cache.set(key, {
      data: value,
      timestamp: Date.now()
    })
  }

  get(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }
}

export const playerCache = new Cache<any>(5) // 5 minutes TTL
export const allianceCache = new Cache<any>(10) // 10 minutes TTL
export const shrineCache = new Cache<any>(1) // 1 minute TTL
