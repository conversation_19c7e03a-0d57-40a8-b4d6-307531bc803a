import { ButtonStyle } from 'discord.js'

import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import { setSettings } from '../../utils/config/config-utils.js'
import { createSkillsPanel } from '../../services/skill/utils/skills-panel-embed.js'
import { logger } from '../../utils/logging/logger.js'

export class HandleGlobalSkillsPanel extends BaseCommandHandler {
  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return

    await this.ensureToken()

    await this.interaction.deferReply({ ephemeral: true })

    try {
      const message = await this.interaction.channel?.send({
        ...(await createSkillsPanel(this.instanceConfig)),
        components: [this.createSkillButtons()]
      })

      if (message) {
        const alertChannelId = this.instanceConfig.skill?.alertChannelId

        this.instanceConfig.skill = {
          alertChannelId: alertChannelId,
          messageId: message.id,
          channelId: message.channel.id
        }

        await setSettings(this.instanceConfig, 'skill.messageId', message.id)
        await setSettings(
          this.instanceConfig,
          'skill.channelId',
          message.channel.id
        )

        await this.interaction.editReply({
          content: 'Global skills panel has been created.'
        })
      }
    } catch (error) {
      logger.error('Error creating global skills panel:', error)
      await this.interaction.editReply({
        content: 'Failed to create global skills panel.'
      })
    }
  }

  private createSkillButtons() {
    return this.messageUtils.createActionRow(
      this.messageUtils.createButton(
        'global-skill-101',
        'Gathering',
        ButtonStyle.Primary
      ),
      this.messageUtils.createButton(
        'global-skill-102',
        'Healing',
        ButtonStyle.Primary
      ),
      this.messageUtils.createButton(
        'global-skill-103',
        'Training',
        ButtonStyle.Primary
      ),
      this.messageUtils.createButton(
        'global-skill-104',
        'Crystal Mine',
        ButtonStyle.Primary
      ),
      this.messageUtils.createButton(
        'refresh-skills',
        'Refresh',
        ButtonStyle.Secondary
      )
    )
  }
}

export default {
  data: {
    name: 'create_skills_panel',
    description: 'Create the global skills panel'
  },
  CommandHandlerClass: HandleGlobalSkillsPanel
}
