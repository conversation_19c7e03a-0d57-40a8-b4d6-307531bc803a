import type { Client } from 'discord.js'
import { BotError } from 'utils/common/error.js'

import type { InstanceConfig } from '../../interfaces/config.js'
import { logger } from '../../utils/logging/logger.js'

import { AllianceManagerMonitor } from './manager/service.js'
import { RotateMonitor } from './rotation/service.js'
import { StakerMonitor } from './staker/staker-monitor.js'
import type { MonitorStatus } from './types.js'

const monitorStatus: Record<string, MonitorStatus> = {}
const allianceManagerMonitors: Record<string, AllianceManagerMonitor> = {}
const rotateMonitors: Record<string, RotateMonitor> = {}

export function setupMonitors(
  client: Client,
  instanceConfig: InstanceConfig
): void {
  try {
    const continent = instanceConfig.continent
    monitorStatus[continent] = {
      rotate: false,
      staker: false,
      manager: false
    }

    if (instanceConfig.staker) {
      const stakerMonitor = new StakerMonitor(client, instanceConfig)
      stakerMonitor.start()
      monitorStatus[continent].staker = true
    }

    if (instanceConfig.rotate?.enabled) {
      const rotateMonitor = new RotateMonitor(client, instanceConfig)
      rotateMonitor.start()
      rotateMonitors[continent] = rotateMonitor
      monitorStatus[continent].rotate = true
    }

    if (instanceConfig.manage?.enabled) {
      const allianceManagerMonitor = new AllianceManagerMonitor(
        client,
        instanceConfig
      )
      allianceManagerMonitor.start()
      allianceManagerMonitors[continent] = allianceManagerMonitor
      monitorStatus[continent].manager = true
    }
  } catch (error) {
    throw new BotError('Failed to setup monitors', 'SETUP_MONITOR_ERROR')
  }
}

export function getMonitorStatus(continent: string): MonitorStatus {
  return (
    monitorStatus[continent] || {
      rotate: false,
      staker: false,
      manager: false
    }
  )
}

export function stopAllMonitors(continent: string): void {
  logger.info(`Stopping all monitors for Continent ${continent}`)

  if (monitorStatus[continent]?.rotate) {
    rotateMonitors[continent]?.stop()
  }

  if (monitorStatus[continent]?.manager) {
    allianceManagerMonitors[continent]?.stop()
  }

  monitorStatus[continent] = {
    rotate: false,
    staker: false,
    manager: false
  }
}
