export type SocketType = "sock" | "socc" | "socf"

export enum ConnectionState {
  DISCONNECTED = "disconnected",
  CONNECTING = "connecting",
  CONNECTED = "connected",
  INITIALIZED = "initialized",
  RECONNECTING = "reconnecting",
  CLOSING = "closing",
  CLOSED = "closed",
}

export type SocketEventType =
  | "connection:open"
  | "connection:close"
  | "connection:error"
  | "message:received"
  | "message:sent"
  | "field:objects"
  | "field:object/update"
  | "instance:created"
  | "instance:closed"

export interface SocketEventPayload {
  [key: string]: unknown
}

export type MessageHandler<T = unknown> = (data: T) => void

export interface SocketOptions {
  pingInterval?: number
  reconnectDelay?: number
  maxReconnectAttempts?: number
  connectionTimeout?: number
  debug?: boolean
}

export interface ConnectionStats {
  messageCount: {
    sent: number
    received: number
  }
  lastActivity: number
  uptime: number
  state: ConnectionState
}

export interface SocketInstanceStats {
  continent: number
  connected: boolean
  connectionCount: number
  messageStats: {
    sent: number
    received: number
  }
  lastActivity: number
}
