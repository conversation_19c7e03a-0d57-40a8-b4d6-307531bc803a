/* eslint-disable @typescript-eslint/no-unused-vars */
import { ButtonInteraction } from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types'

import type { InstanceConfig } from '../../../interfaces/config'
import type { ApiService } from '../../../services/api/api-service'
import { AllianceManagerMonitor } from '../../../services/monitor/manager/service'
import { setSettings } from '../../../utils/config/config-utils'
import { updateManagerPanel } from '../../../services/monitor/manager/utils/manager-panel-embed'
import { logger } from '../../../utils/logging/logger'

export async function handleEnableManager(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap
): Promise<void> {
  try {
    await interaction.deferUpdate()

    const managerMonitor = new AllianceManagerMonitor(
      interaction.client,
      instanceConfig
    )
    await managerMonitor.start()
    instanceConfig.manage.enabled = true
    await setSettings(instanceConfig, 'manage.enabled', true)

    await updateManagerPanel(interaction.client, instanceConfig)

    await interaction.followUp({
      content: 'Alliance manager has been enabled.',
      ephemeral: true
    })

    logger.info(
      `Alliance manager enabled for continent ${instanceConfig.continent}`
    )
  } catch (error) {
    logger.error('Error enabling alliance manager:', error)
    await interaction.followUp({
      content: 'Failed to enable alliance manager.',
      ephemeral: true
    })
  }
}
