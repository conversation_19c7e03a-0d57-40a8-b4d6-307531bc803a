# Discord Bot Startup Flow

## Overview
The startup flow is the critical initialization sequence that brings the Discord bot online, establishes connections to external services, and prepares all systems for operation.

## Entry Point
- **File**: `src/index.ts`
- **Function**: `main()`
- **Trigger**: Process start

## Flow Sequence

### 1. Configuration Loading
```typescript
// src/utils/config/config-loader.ts
const botConfig = await loadConfig()
```

**Process:**
- Searches for `config.json` in multiple locations:
  - `{cwd}/config/config.json`
  - `{project}/config/config.json`
  - Relative paths from current directory
- Parses JSON configuration
- Extracts bot token, client ID, and instance configurations
- **Error Handling**: Throws if config file not found

### 2. Service Registry Initialization
```typescript
// src/core/service-registry.ts
ServiceRegistry.getInstance()
```

**Purpose:**
- Centralizes service instance management
- Replaces inconsistent singleton patterns
- Manages per-continent service instances

### 3. Instance Initialization Loop
```typescript
for (const instance of botConfig.instances) {
  const result = await initializeClient(instanceConfig)
}
```

**Per Instance Process:**

#### 3.1 Authentication
```typescript
// src/services/auth/login-service.ts
const loginResult = await login(instanceConfig)
```
- Authenticates with game servers
- Obtains access token and XOR encryption key
- **Critical**: Required for all subsequent API calls

#### 3.2 Kingdom Entry
```typescript
// src/services/kingdom/kingdom-service.ts
const kingdomResponse = await enterKingdom(instanceConfig)
```
- Calls `/kingdom/enter` API endpoint
- Retrieves network URLs for socket connections
- Stores kingdom data in instance configuration

#### 3.3 Socket Service Initialization
```typescript
// src/services/socket/service.ts
await socketService.initialize(["socc", "sock", "socf"])
```

**Socket Types:**
- **socc**: Chat socket (no token required)
- **sock**: Kingdom socket (requires token)
- **socf**: Field socket (requires token + location data)

**Connection Sequence:**
1. Create WebSocket connections with retry logic
2. Handle connection state changes
3. Register message handlers
4. Initialize field zones for socf

#### 3.4 Service Registration
```typescript
// src/core/service-registry.ts
registerApiService(continent, apiService, instanceConfig)
registerSocketService(continent, socketService, instanceConfig)
```

### 4. Command System Initialization
```typescript
// src/utils/discord/command-manager.ts
await commandManager.loadCommands()
```

**Process:**
- Loads all command handlers from `src/commands/`
- Registers commands in internal collection
- Validates command structure

### 5. Discord Command Deployment
```typescript
// src/deploy-commands.ts
await deployCommands(botConfig)
```
- Registers slash commands with Discord API
- Updates command definitions if changed

### 6. Discord Client Setup
```typescript
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent
  ]
})
```

### 7. Event Handler Registration
```typescript
client.on('interactionCreate', async (interaction) => {
  await handleInteraction(interaction, titleToggleMap, roleStatusMap)
})
```

### 8. Monitor Services Setup
```typescript
// src/services/monitor/service.ts
setupMonitors(client, instanceConfig)
```

**Monitor Types:**
- **Staker Monitor**: 24-hour interval for staker updates
- **Rotation Monitor**: 30-minute interval for alliance rotation
- **Alliance Manager**: Continuous alliance management

### 9. Bot Login
```typescript
await client.login(bot_token)
```

### 10. Graceful Shutdown Handlers
```typescript
process.on('SIGINT', async () => {
  closeAllSocketServices()
  process.exit(0)
})
```

## Error Handling

### Critical Failures
- **Config Loading**: Process exits if config not found
- **Authentication**: Continues with other instances if one fails
- **Socket Connection**: Retries with exponential backoff
- **Discord Login**: Process exits if Discord connection fails

### Recovery Mechanisms
- **Socket Reconnection**: Automatic reconnection with retry limits
- **API Token Refresh**: Automatic re-authentication on token expiry
- **Service Isolation**: Instance failures don't affect other instances

## Dependencies

### External Services
- **Discord API**: Bot token validation and command registration
- **Game Servers**: Authentication and socket connections
- **Configuration Files**: JSON configuration loading

### Internal Dependencies
- Service Registry for centralized management
- Command Manager for dynamic command loading
- Logger for comprehensive logging
- Error handlers for graceful failure management

## Success Criteria
- All configured instances successfully authenticated
- Socket connections established for all required types
- Commands loaded and registered with Discord
- Monitor services started where configured
- Bot status shows as online in Discord

## Performance Considerations
- **Parallel Instance Initialization**: Instances initialize concurrently
- **Connection Pooling**: Reuses socket connections where possible
- **Retry Logic**: Prevents cascade failures from temporary issues
- **Resource Cleanup**: Proper cleanup on shutdown signals

## Monitoring Points
- Configuration loading success/failure
- Per-instance authentication status
- Socket connection health
- Command registration status
- Monitor service startup status
- Overall bot online status
