import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import {
 getIds, fetchCvcRanks 
} from '../../services/cvc/cvc-service.js'
import {
  getAllPlayerProfiles,
  addOrUpdatePlayerProfile
} from '../../services/storage/user-data-storage.js'
import { logger } from '../../utils/logging/logger.js'

export class HandleUpdate extends BaseCommandHandler {
  async execute(): Promise<void> {
    try {
      await this.interaction.deferReply()

      await this.ensureToken()

      const { individualId, continentId, isCvcActive } = await getIds(
        this.instanceConfig
      )

      if (!isCvcActive) {
        await this.interaction.followUp({
          content: 'CVC is not currently active. No data to update.'
        })
        return
      }

      logger.info(
        `Individual ID: ${individualId}, Continent ID: ${continentId}`
      )

      const rankData = await fetchCvcRanks(
        this.instanceConfig,
        individualId!,
        continentId!
      )

      if (rankData.length === 0) {
        await this.interaction.followUp({
          content: 'No CVC data retrieved.'
        })
        return
      }

      logger.info(`Retrieved rank data for ${rankData.length} kingdoms`)

      const allProfiles = await getAllPlayerProfiles(this.instanceConfig)
      let updatedCount = 0

      for (const [discordId, profiles] of Object.entries(allProfiles)) {
        for (const profile of profiles) {
          const rankInfo = rankData.find(r => r.id === profile.id)
          if (rankInfo) {
            const updatedProfile = {
              ...profile,
              individualRank: rankInfo.individualRank,
              continentRank: rankInfo.continentRank,
              individualPoints: rankInfo.individualPoints
            }
            await addOrUpdatePlayerProfile(
              this.instanceConfig,
              discordId,
              updatedProfile
            )
            updatedCount++
          }
        }
      }

      logger.info(
        `[HandleUpdate] User data successfully updated. Updated ${updatedCount} profiles.`
      )

      await this.interaction.followUp({
        content: `CVC data has been updated successfully! Updated ${updatedCount} profiles.`
      })
    } catch (error) {
      logger.error(
        '[HandleUpdate] Failed to update CVC data:',
        error instanceof Error ? error.message : String(error)
      )
      await this.handleError(
        error,
        'An error occurred while updating CVC data. Please check the logs for more information.'
      )
    }
  }
}

export default {
  data: {
    name: 'update_data',
    description: 'Update CVC data for all stored profiles'
  },
  CommandHandlerClass: HandleUpdate
}
