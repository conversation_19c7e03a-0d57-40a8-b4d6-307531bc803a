import type { InstanceConfig } from '../../../interfaces/config.js'

import { setSettings } from '../../../utils/config/config-utils.js'

export function updateTitleRequestCounters(
  instanceConfig: InstanceConfig
): void {
  const currentDate = new Date().toISOString().split('T')[0]

  if (instanceConfig.title.lastResetDate !== currentDate) {
    instanceConfig.title.dailyRequests = 0
    instanceConfig.title.lastResetDate = currentDate
  }

  instanceConfig.title.dailyRequests++
  instanceConfig.title.totalRequests++

  setSettings(
    instanceConfig,
    'title.dailyRequests',
    instanceConfig.title.dailyRequests
  )
  setSettings(
    instanceConfig,
    'title.totalRequests',
    instanceConfig.title.totalRequests
  )
  setSettings(
    instanceConfig,
    'title.lastResetDate',
    instanceConfig.title.lastResetDate
  )
}
