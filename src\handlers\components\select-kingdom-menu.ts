import type { StringSelectMenuInteraction } from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'
import { assignTitle } from '../../services/title/service.js'
import { updateTitlePanelEmbed } from '../../services/title/utils/title-panel-embed.js'

export async function handleKingdomSelection(
  interaction: StringSelectMenuInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap
): Promise<void> {
  const [_, __, role] = interaction.customId.split('-')
  const kingdomId = interaction.values[0]

  try {
    const result = await assignTitle(
      role,
      kingdomId,
      instanceConfig,
      roleStatusMap
    )

    if (result.result) {
      await interaction.reply({
        content: `${role} role assigned to kingdom ${kingdomId}`,
        ephemeral: true
      })
      await updateTitlePanelEmbed(
        interaction.client,
        instanceConfig,
        roleStatusMap,
        titleToggleMap
      )
    } else {
      await interaction.reply({
        content: `Failed to assign ${role} role: ${result.message}`,
        ephemeral: true
      })
    }
  } catch (error) {
    console.error('Error assigning title:', error)
    await interaction.reply({
      content: 'An error occurred while assigning the role.',
      ephemeral: true
    })
  }
}
