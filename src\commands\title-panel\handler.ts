import {
  type ChatInputCommandInteraction,
  type <PERSON>Row<PERSON>uilder,
  type <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ButtonStyle
} from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'
import { updateTitlePanelEmbed } from '../../services/title/utils/title-panel-embed.js'

export class HandleTitlePanel extends BaseCommandHandler {
  constructor(
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap: TitleToggleMap,
    roleStatusMap: RoleStatusMap
  ) {
    super(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap
    )
  }

  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return

    await this.ensureToken()

    await this.interaction.deferReply({ ephemeral: true })

    const actionRow = this.createTitlePanelButtons()

    try {
      const message = await this.interaction.channel?.send({
        components: [actionRow]
      })

      if (message) {
        this.instanceConfig.title.messageId = message.id
        this.instanceConfig.title.channelId = message.channel.id

        await updateTitlePanelEmbed(
          this.interaction.client,
          this.instanceConfig,
          this.roleStatusMap,
          this.titleToggleMap
        )

        await this.interaction.editReply({
          content: 'Title panel has been created and updated.'
        })
      } else {
        throw new Error('Failed to create title panel message')
      }
    } catch (error) {
      this.logger.error('Error creating title panel:', error)
      await this.interaction.editReply({
        content: 'Failed to create title panel.'
      })
    }
  }

  private createTitlePanelButtons(): ActionRowBuilder<ButtonBuilder> {
    return this.messageUtils.createActionRow(
      this.messageUtils.createButton(
        'title-alchemist',
        'Alchemist',
        ButtonStyle.Primary
      ),
      this.messageUtils.createButton(
        'title-architect',
        'Architect',
        ButtonStyle.Primary
      ),
      this.messageUtils.createButton(
        'expire-role',
        'Expire Role',
        ButtonStyle.Danger
      ),
      this.messageUtils.createButton(
        'title-add-kingdom',
        'Add Kingdom',
        ButtonStyle.Secondary
      )
    )
  }
}

export default {
  data: {
    name: 'create_title_panel',
    description: 'Create the title request panel'
  },
  CommandHandlerClass: HandleTitlePanel
}
