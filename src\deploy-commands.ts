import {
 REST, Routes, type ApplicationCommandDataResolvable 
} from 'discord.js'

import type { BotConfig } from './interfaces/config.js'
import { commandManager } from './utils/discord/command-manager.js'
import type { Command } from './utils/discord/types.js'
import { logger } from './utils/logging/logger.js'

export async function deployCommands(botConfig: BotConfig): Promise<void> {
  await commandManager.loadCommands()
  const commands = commandManager.getCommands()

  const rest = new REST({ version: '10' }).setToken(botConfig.bot_token)

  try {
    for (const instance of botConfig.instances) {
      const enabledCommands = Array.from(commands.values())
        .filter((cmd: Command) => {
          const isEnabled = instance.enabledCommands.includes(cmd.data.name)
          if (!isEnabled) {
            logger.debug(
              `Command ${cmd.data.name} is not enabled for guild ${instance.guild_id}`
            )
          }
          return isEnabled
        })
        .map((cmd: Command) => {
          return cmd.data as ApplicationCommandDataResolvable
        })

      const data = await rest.put(
        Routes.applicationGuildCommands(botConfig.client_id, instance.guild_id),
        {
          body: enabledCommands
        }
      )

      logger.info(
        `Successfully deployed ${(data as unknown[]).length} commands for guild ${instance.guild_id} (Continent ${instance.continent})`
      )
    }
  } catch (error) {
    logger.error('Error deploying commands:', error)
    throw error
  }
}
