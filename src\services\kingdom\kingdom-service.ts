import type { InstanceConfig } from '../../interfaces/config.js'
import type { KingdomData } from '../../interfaces/player.js'
import { BotError } from '../../utils/common/error.js'
import { logger } from '../../utils/logging/logger.js'
import { getApiInstance } from '../api/api-instance.js'

export async function enterKingdom(
  instanceConfig: InstanceConfig
): Promise<KingdomData> {
  const api = getApiInstance(instanceConfig.continent)

  logger.info(
    `[KingdomService] Entering kingdom for Continent ${instanceConfig.continent}`
  )

  try {
    const response = await api.post<KingdomData>('/kingdom/enter', {})

    if (!response.kingdom || !response.networks) {
      throw new BotError(
        'Invalid kingdom/enter response',
        'KINGDOM_ENTER_ERROR'
      )
    }

    instanceConfig.kingdomData = response

    logger.info(
      `[KingdomService] Successfully entered kingdom ${response.kingdom.name} (${response.kingdom._id})`
    )
    logger.debug(`[KingdomService] Kingdom data:`, {
      networks: response.networks,
      kingdom: {
        id: response.kingdom._id,
        name: response.kingdom.name,
        loc: response.kingdom.loc
      }
    })

    return response
  } catch (error) {
    logger.error(
      `[KingdomService] Failed to enter kingdom for Continent ${instanceConfig.continent}:`,
      error
    )
    throw error instanceof BotError
      ? error
      : new BotError('Failed to enter kingdom', 'KINGDOM_ENTER_ERROR')
  }
}