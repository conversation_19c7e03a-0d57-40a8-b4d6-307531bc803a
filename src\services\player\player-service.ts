import { InstanceConfig } from 'interfaces/config'
import { BotError } from 'utils/common/error.js'

import {
  PlayerProfile,
  PlayerHistoryResponse,
  PlayerProfileResponse
} from '../../interfaces/player.js'
import { getApiInstance } from '../api/api-instance'

export async function fetchPlayerProfile(
  instanceConfig: InstanceConfig,
  kingdomId: string
): Promise<PlayerProfile | null> {
  const api = getApiInstance(instanceConfig.continent)

  const response1 = await api.post<PlayerProfileResponse>(
    '/kingdom/profile/other',
    { kingdomId }
  )

  const response2 = await api.post<PlayerHistoryResponse>(
    '/kingdom/profile/other/history',
    { kingdomId }
  )

  if (!response1.profile || !response2.history) {
    throw new BotError('Error fecthing player profile', 'PLAYER_PROFILE_ERROR')
  }

  return {
    name: response1.profile.name,
    id: response1.profile._id,
    continent: response1.profile.worldId,
    power: response2.history.power.total,
    kill: response2.history.stats.battle?.kill,
    death: response2.history.stats.battle?.death,
    gathering: response2.history.stats.economy?.gathering
  }
}
