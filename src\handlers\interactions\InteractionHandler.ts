import {
 type Interaction, InteractionType 
} from 'discord.js'
import { SocketService } from 'services/socket/service.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'
import { logger } from '../../utils/logging/logger.js'

import { handleButton } from './ButtonInteractions.js'
import { handleCommand } from './CommandInteractions.js'
import { handleSelectMenu } from './SelectMenuInteractions.js'

export type InteractionHandlerFunction = (
  interaction: Interaction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap,
  socketService?: SocketService | null
) => Promise<void>

const interactionHandlers = new Map<
  InteractionType,
  InteractionHandlerFunction
>([
  [
    InteractionType.ApplicationCommand,
    handleCommand as InteractionHandlerFunction
  ],
  [
    InteractionType.MessageComponent,
    async (interaction, ...args) => {
      if (interaction.isButton()) {
        return handleButton(interaction, ...args)
      }
      if (interaction.isStringSelectMenu()) {
        return handleSelectMenu(interaction, ...args)
      }
    }
  ]
])

export async function handleInteraction(
  interaction: Interaction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap,
  socketService: SocketService | null
): Promise<void> {
  const handler = interactionHandlers.get(interaction.type)
  if (!handler) {
    logger.warn(`No handler found for interaction type: ${interaction.type}`)
    return
  }

  try {
    await handler(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap,
      socketService
    )
  } catch (error) {
    logger.error(`Error handling ${interaction.type} interaction:`, error)
    if (
      interaction.isRepliable() &&
      !interaction.replied &&
      !interaction.deferred
    ) {
      try {
        await interaction.reply({
          content: 'An error occurred while processing your request.',
          ephemeral: true
        })
      } catch (replyError) {
        logger.error('Error sending error reply:', replyError)
      }
    } else if (interaction.isRepliable()) {
      try {
        await interaction.editReply(
          'An error occurred while processing your request.'
        )
      } catch (editReplyError) {
        logger.error('Error editing reply with error message:', editReplyError)
      }
    }
  }
}
