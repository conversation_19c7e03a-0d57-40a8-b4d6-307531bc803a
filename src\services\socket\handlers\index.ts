import { logger } from "../../../utils/logging/logger.js"
import type { SocketService } from "../service.js"

import { handleFieldObject } from './field-objects-handler.js';

export function registerMessageHandlers(socketService: SocketService): void {
  logger.info("[SocketHandlers] Registering message handlers")

  // Register handler for field object updates - primary path
  logger.info("[FIELD_OBJECT_UPDATE] Registering primary handler for /field/object/update")
  socketService.onMessage("socf", "/field/object/update", (data) => {
    logger.info("[FIELD_OBJECT_UPDATE] SocketHandler received message")
    handleFieldObject(data)
  })

  // Register handler for field objects - might contain kingdom updates too
  logger.info("[FIELD_OBJECT_UPDATE] Registering secondary handler for /field/objects/v4")
  socketService.onMessage("socf", "/field/objects/v4", (data) => {
    logger.info("[FIELD_OBJECT_UPDATE] SocketHandler received /field/objects/v4 message")
    // This might contain multiple objects including kingdoms
    const objectsData = data as { objects?: any[] }
    if (objectsData && objectsData.objects && objectsData.objects.length > 0) {
      // Process each object that might be a kingdom
      objectsData.objects.forEach(obj => {
        if (obj && obj.code === 20300101) {
          logger.info("[FIELD_OBJECT_UPDATE] SocketHandler found kingdom in /field/objects/v4 message")
          handleFieldObject({ fo: obj })
        }
      })
    }
  })

  // Register a catch-all handler for debugging
  logger.info("[FIELD_OBJECT_UPDATE] Registering catch-all handler for field object messages")
  socketService.onMessage("socf", "*", (data) => {
    const path = (data as any)?.path
    if (path && path.includes("field") && path.includes("object")) {
      logger.info(`[FIELD_OBJECT_UPDATE] SocketHandler caught potential message: ${path}`)
      logger.debug(`[FIELD_OBJECT_UPDATE] Message data: ${JSON.stringify(data)}`)
    }
  })

  logger.info("[SocketHandlers] Message handlers registered")

  // Verify registration
  setTimeout(() => {
    const eventName = "message:socf:/field/object/update"
    const listenerCount = socketService.listenerCount(eventName)
    logger.info(`[FIELD_OBJECT_UPDATE] Verification: ${listenerCount} listeners registered for ${eventName}`)
  }, 1000)
}

