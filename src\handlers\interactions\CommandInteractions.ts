import type { ChatInputCommandInteraction } from 'discord.js'
import { SocketService } from 'services/socket/service.js'
import type {
 RoleStatusMap, TitleToggleMap
} from 'services/title/types.js'

import type { InstanceConfig } from '../../interfaces/config.js'
import { createCommandContext } from '../../interfaces/context.js'
import type { ApiService } from '../../services/api/api-service.js'
import { commandManager } from '../../utils/discord/command-manager.js'
import { InteractionErrorHandler } from '../../utils/discord/interaction-error-handler.js'
import { logger } from '../../utils/logging/logger.js'

export async function handleCommand(
  interaction: ChatInputCommandInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap,
  socketService?: SocketService | null
): Promise<void> {
  const { commandName } = interaction
  const command = commandManager.getCommand(commandName)

  if (!command) {
    logger.warn(`Command not found: ${commandName}`)
    await InteractionErrorHandler.handleError(
      interaction,
      new Error(`Command not found: ${commandName}`),
      { customMessage: 'Command not found' }
    )
    return
  }

  try {
    // Create context object for new pattern, with fallback to legacy constructor
    const context = createCommandContext(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap,
      socketService
    )

    // Try context-based constructor first, fallback to legacy
    let handler
    try {
      handler = new command.CommandHandlerClass(context)
    } catch {
      // Fallback to legacy constructor pattern
      handler = new command.CommandHandlerClass(
        interaction,
        instanceConfig,
        apiInstance,
        titleToggleMap,
        roleStatusMap,
        socketService
      )
    }

    await handler.execute()
  } catch (error) {
    await InteractionErrorHandler.handleCommandError(
      interaction,
      error,
      commandName
    )
  }
}
