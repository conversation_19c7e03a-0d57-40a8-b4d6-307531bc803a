import type { ChatInputCommandInteraction } from 'discord.js'
import { SocketService } from 'services/socket/service.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'
import { commandManager } from '../../utils/discord/command-manager.js'
import { logger } from '../../utils/logging/logger.js'

export async function handleCommand(
  interaction: ChatInputCommandInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap,
  socketService?: SocketService | null
): Promise<void> {
  const { commandName } = interaction
  const command = commandManager.getCommand(commandName)

  if (!command) {
    logger.warn(`Command not found: ${commandName}`)
    await interaction.reply({
      content: 'Command not found',
      ephemeral: true
    })
    return
  }

  try {
    const handler = new command.CommandHandlerClass(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap,
      socketService
    )
    await handler.execute()
  } catch (error) {
    logger.error(`Error executing command ${commandName}:`, error)
    await interaction.reply({
      content: 'There was an error while executing this command!',
      ephemeral: true
    })
  }
}
