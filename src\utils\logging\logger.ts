import winston from 'winston'

const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
}

const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
}

winston.addColors(colors)

const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
)

const safeStringify = (obj: unknown, maxLength = 1000): string => {
  if (obj === null || obj === undefined) {
    return String(obj)
  }

  if (typeof obj === 'string') {
    return obj.length > maxLength ? `${obj.substring(0, maxLength)}...` : obj
  }

  if (obj instanceof Buffer) {
    return '[Buffer]'
  }

  if (obj instanceof Error) {
    return obj.stack || obj.message
  }

  try {
    const stringified = JSON.stringify(
      obj,
      (key, value) => {
        if (typeof value === 'object' && value !== null) {
          if (value instanceof Buffer) {
            return '[Buffer]'
          }
          if (value instanceof Error) {
            return value.message
          }
        }
        return value
      },
      2
    )

    if (stringified.length > maxLength) {
      return `${stringified.substring(0, maxLength)}...`
    }

    return stringified
  } catch (error) {
    return '[Complex object that could not be stringified]'
  }
}

const consoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, ...metadata }) => {
    const formattedMessage =
      typeof message === 'string' ? message : safeStringify(message)

    let msg = `${timestamp} [${level}]: ${formattedMessage}`

    if (Object.keys(metadata).length > 0) {
      const cleanMetadata = { ...metadata }

      if (cleanMetadata.stack) {
        delete cleanMetadata.stack
      }

      if (Object.keys(cleanMetadata).length > 0) {
        msg += '\n' + safeStringify(cleanMetadata)
      }
    }

    return msg
  })
)

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels: logLevels,
  format: logFormat,
  transports: [new winston.transports.Console({ format: consoleFormat })]
})

export { logger }
