import type { InstanceConfig } from 'interfaces/config.js'

import type {
  AllianceInfo,
  AllianceMembersResponse
} from '../../interfaces/alliance.js'
import type {
  PlayerHistoryResponse,
  PlayerProfile,
  PlayerProfileResponse
} from '../../interfaces/player.js'
import { BotError } from '../../utils/common/error.js'
import { getApiInstance } from '../api/api-instance.js'

export async function fetchAllianceMembers(
  instanceConfig: InstanceConfig,
  allyID: string
): Promise<AllianceInfo> {
  const api = getApiInstance(instanceConfig.continent)

  try {
    const response = await api.post<AllianceMembersResponse>(
      '/alliance/members/list',
      { allianceId: allyID }
    )

    if (!Array.isArray(response.members)) {
      throw new BotError('Invalid alliance members data', 'ALLIANCE_DATA_ERROR')
    }

    const allMembers = response.members.flatMap(group => group.members)

    const allianceInfo: AllianceInfo = {
      members: allMembers
    }

    return allianceInfo
  } catch (error) {
    throw error instanceof BotError
      ? error
      : new BotError('Failed to fetch alliance members', 'ALLIANCE_FETCH_ERROR')
  }
}

export async function fetchPlayerProfile(
  instanceConfig: InstanceConfig,
  kingdomId: string
): Promise<PlayerProfile> {
  const api = getApiInstance(instanceConfig.continent)

  try {
    const response1 = await api.post<PlayerProfileResponse>(
      '/kingdom/profile/other',
      { kingdomId }
    )

    const response2 = await api.post<PlayerHistoryResponse>(
      '/kingdom/profile/other/history',
      { kingdomId }
    )

    if (!response1.profile || !response2.history) {
      throw new BotError('Invalid player profile data', 'PLAYER_PROFILE_ERROR')
    }

    const profile: PlayerProfile = {
      name: response1.profile.name,
      id: response1.profile._id,
      continent: response1.profile.worldId,
      power: response2.history.power.total,
      kill: response2.history.stats.battle?.kill,
      death: response2.history.stats.battle?.death,
      gathering: response2.history.stats.economy?.gathering
    }

    return profile
  } catch (error) {
    throw error instanceof BotError
      ? error
      : new BotError('Failed to fetch player profile', 'PLAYER_FETCH_ERROR')
  }
}
