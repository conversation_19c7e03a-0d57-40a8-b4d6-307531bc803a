import type { Zone, MapSearchParams } from './types'
import { ZoneManager } from './zone-manager'
import { ZoneNetwork } from './zone-network'
import { SearchStrategy } from './search-strategy'
import { wait } from '../../../utils/timer'

export function mapSearch({
  autoRun,
  searchDistance,
  socketWrap,
  token,
  xorKey,
  kingdom
}: MapSearchParams) {
  let running = autoRun
  const zoneManager = new ZoneManager()
  const zoneNetwork = new ZoneNetwork(
    socketWrap,
    token,
    xorKey,
    kingdom.loc.worldId,
    kingdom._id
  )
  const searchStrategy = new SearchStrategy(searchDistance)

  async function searchZones(client) {
    if (!running) return

    if (!zoneManager.hasZones()) {
      zoneManager.initialize(kingdom.loc.x, kingdom.loc.y)
    }

    const spiralZones = searchStrategy.getNextZones(
      client,
      kingdom.loc.x,
      kingdom.loc.y
    )
    const centerZone = spiralZones.center

    if (client.processedZones.has(centerZone)) {
      client.logger.info(
        `Zone ${centerZone} already processed. Moving to next.`
      )
      searchStrategy.incrementStep(client)
      return
    }

    client.processedZones.add(centerZone)
    client.logger.info(
      `processedZones: ${Array.from(client.processedZones).join(', ')}`
    )

    // const exitZones = [centerZone - 1, centerZone, centerZone + 1].filter(
    //   id => id >= 0
    // )
    // await zoneNetwork.exitZones(exitZones)

    searchStrategy.incrementStep(client)

    await zoneNetwork.enterZones(spiralZones.zones)

    if (searchStrategy.isComplete(client)) {
      client.logger.warn('All zones processed. Stopping search.')
      running = false
      return
    }

    // "shouldTakeBreak" needs to be changed to release/close the socketio socket then recreate a new one. 
    if (searchStrategy.shouldTakeBreak(client)) {
      await wait(5000)
      await client.enterKingdom()
      client.logger.info(`Taking a break...`)

      searchStrategy.saveProgress(client)
      running = false
      return
    }

    await searchStrategy.handleDelays()
  }

  return {
    searchZones,
    isStarted: () => running,
    toggle: client => {
      running = !running
      client.logger.info('Search Toggled. Running State:', running)
      return running
    },
    addZone: (zone: Zone, client) => zoneManager.addZone(zone, client),
    removeZone: (zone: Zone, client) => zoneManager.removeZone(zone, client),
    resumeSearch: client => {
      searchStrategy.resumeFromLastSaved(client)
      running = true
      client.logger.info('Resuming search from last saved position.')
    }
  }
}
