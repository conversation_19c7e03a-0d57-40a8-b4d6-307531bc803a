import { logger } from "../../../utils/logging/logger.js"

export const MessageCategories = {
  IGNORED: new Set(["/march/object/update", "/chat/new"]),
  ENCODED: new Set([
    "/field/objects/v4",
    "/field/enter/v3",
    "/field/object/update",
    "/march/objects",
    "/zone/enter/list/v4",
  ]),
  MONITORED: new Set([
    "/field/objects/v4",
    "/march/objects",
    "/field/object/update"
  ]),
}

export class MessageRouter {
  private handlers = new Map<string, Set<(data: unknown) => void>>()

  public register(path: string, handler: (data: unknown) => void) {
    if (!this.handlers.has(path)) this.handlers.set(path, new Set())
    this.handlers.get(path)!.add(handler)

    if (path === "/field/object/update") {
      logger.info(`[FIELD_OBJECT_UPDATE] MessageRouter registered handler (total: ${this.handlers.get(path)!.size})`)
    } else {
      logger.debug(`[MessageRouter] Registered handler for ${path}`)
    }
  }

  public unregister(path: string, handler?: (data: unknown) => void) {
    if (!this.handlers.has(path)) return

    if (handler) {
      this.handlers.get(path)!.delete(handler)

      if (path === "/field/object/update") {
        const remainingHandlers = this.handlers.get(path)?.size || 0
        logger.info(`[FIELD_OBJECT_UPDATE] MessageRouter unregistered specific handler (remaining: ${remainingHandlers})`)
      } else {
        logger.debug(`[MessageRouter] Unregistered specific handler for ${path}`)
      }
    } else {
      this.handlers.delete(path)

      if (path === "/field/object/update") {
        logger.info(`[FIELD_OBJECT_UPDATE] MessageRouter unregistered ALL handlers!`)
      } else {
        logger.debug(`[MessageRouter] Unregistered all handlers for ${path}`)
      }
    }
  }

  public route(path: string, data: unknown): boolean {
    if (MessageCategories.IGNORED.has(path)) {
      logger.debug(`[MessageRouter] Ignoring message for path: ${path}`)
      return false
    }

    const handlers = this.handlers.get(path)

    if (handlers?.size) {
      logger.debug(`[MessageRouter] Found ${handlers.size} handlers for path: ${path}`)

      // Special logging for field/object/update
      if (path === "/field/object/update") {
        logger.info(`[FIELD_OBJECT_UPDATE] MessageRouter routing message with data: ${JSON.stringify(data)}`)
      }

      let handlerIndex = 0
      for (const handler of handlers) {
        handlerIndex++
        try {
          if (path === "/field/object/update") {
            logger.info(`[FIELD_OBJECT_UPDATE] MessageRouter executing handler ${handlerIndex}/${handlers.size}`)
          } else {
            logger.debug(`[MessageRouter] Executing handler ${handlerIndex}/${handlers.size} for ${path}`)
          }

          handler(data)

          if (path === "/field/object/update") {
            logger.info(`[FIELD_OBJECT_UPDATE] MessageRouter handler ${handlerIndex}/${handlers.size} completed successfully`)
          } else {
            logger.debug(`[MessageRouter] Handler ${handlerIndex}/${handlers.size} for ${path} completed successfully`)
          }
        } catch (error) {
          if (path === "/field/object/update") {
            logger.error(`[FIELD_OBJECT_UPDATE] MessageRouter error in handler ${handlerIndex}/${handlers.size}:`, error)
          } else {
            logger.error(`[MessageRouter] Error in handler ${handlerIndex}/${handlers.size} for ${path}:`, error)
          }
        }
      }
      return true
    } else {
      // Special logging for field/object/update
      if (path === "/field/object/update") {
        logger.warn(`[FIELD_OBJECT_UPDATE] MessageRouter has NO HANDLERS registered for this path!`)
      } else {
        logger.debug(`[MessageRouter] No handlers registered for path: ${path}`)
      }
      return false
    }
  }

  public hasHandlers(path: string): boolean {
    return this.handlers.has(path) && !!this.handlers.get(path)!.size
  }

  public getRegisteredPaths(): string[] {
    return Array.from(this.handlers.keys())
  }
}

