export interface ShrineObject {
  type: number
  loc: number[]
  worldId: number
  open: {
    started: string
  }
}

export interface ProcessedShrine {
  type: number
  coords: number[]
  startTime: Date
  timeRemaining: number
  controllingContinent: number | string
}

export interface ShrineType {
  name: string
  emoji: string
}

export const shrineTypes: { [key: number]: ShrineType } = {
  11: { name: 'Ancient Temple', emoji: '🏛️' },
  12: { name: 'Fortress A', emoji: '🏰' },
  13: { name: 'Fortress B', emoji: '🏯' },
  14: { name: 'Gate 1', emoji: ':regional_indicator_g::one:' },
  15: { name: 'Gate 2', emoji: ':regional_indicator_g::two:' },
  16: { name: 'Gate 3', emoji: ':regional_indicator_g::three:' },
  17: { name: 'Gate 4', emoji: ':regional_indicator_g::four:' },
  18: { name: 'Gate 5', emoji: ':regional_indicator_g::five:' }
}
