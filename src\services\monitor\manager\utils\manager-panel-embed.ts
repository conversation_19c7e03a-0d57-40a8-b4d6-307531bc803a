import {
 type Client, EmbedBuilder, type TextChannel 
} from 'discord.js'
import type { InstanceConfig } from 'interfaces/config'
import { logger } from 'utils/logging/logger'

export function createManagerPanel(instanceConfig: InstanceConfig) {
  const embed = new EmbedBuilder()
    .setTitle('Alliance Manager Panel')
    .setDescription(
      `Status: ${instanceConfig.manage.enabled ? '🟢 Enabled' : '🔴 Disabled'}\n\n` +
        `**Current Settings:**\n` +
        `Minimum Power: ${instanceConfig.manage.minPower || 'Not Set'}\n\n` +
        `**How it works:**\n` +
        `• Automatically processes alliance applications\n` +
        `• Accepts players above minimum power\n` +
        `• Denies players below minimum power\n` +
        `• Sends notification mail to denied applicants`
    )
    .setTimestamp()
    .setColor(instanceConfig.manage.enabled ? 0x00ff00 : 0xff0000)

  return { embeds: [embed] }
}

export async function updateManagerPanel(
  client: Client,
  instanceConfig: InstanceConfig
) {
  if (!instanceConfig.manage?.channelId || !instanceConfig.manage?.messageId) {
    return
  }

  try {
    const channel = (await client.channels.fetch(
      instanceConfig.manage.channelId
    )) as TextChannel
    if (!channel || !channel.isTextBased()) {
      logger.error('Invalid manager panel channel configuration.')
      return
    }

    const message = await channel.messages.fetch(
      instanceConfig.manage.messageId
    )
    await message.edit(createManagerPanel(instanceConfig))
  } catch (error) {
    logger.error('Error updating manager panel:', error)
  }
}
