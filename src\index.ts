import {
 Client, GatewayIntentBits, type Interaction 
} from 'discord.js'
import { handleInteraction } from 'handlers/interactions/InteractionHandler.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import {
  ServiceRegistry,
  ServiceType,
  registerApiService,
  registerSocketService,
  getApiService,
  getSocketService as getRegistrySocketService,
  hasSocketService as hasRegistrySocketService
} from './core/service-registry.js'
import { deployCommands } from './deploy-commands.js'
import type { InstanceMap } from './interfaces/config.js'
import { getApiInstance } from './services/api/api-instance.js'
import {
  initializeClient,
  getSocketService,
  hasSocketService,
  closeAllSocketServices
} from './services/client/client-init.js'
import { loadConfig } from './utils/config/config-loader.js'
import { commandManager } from './utils/discord/command-manager.js'
import { logger } from './utils/logging/logger.js'

const titleToggleMap: InstanceMap<TitleToggleMap> = {}
const roleStatusMap: InstanceMap<RoleStatusMap> = {}

logger.level = process.env.NODE_ENV === 'production' ? 'info' : 'debug'

async function main() {
  try {
    const botConfig = await loadConfig()
    const { bot_token, instances } = botConfig

    let loginSuccessful = false

    for (const instance of instances) {
      try {
        const result = await initializeClient(instance)

        if (result.success) {
          loginSuccessful = true
          titleToggleMap[instance.continent] = { [instance.continent]: true }
          roleStatusMap[instance.continent] = {}

          // Register services in the unified registry
          const apiInstance = getApiInstance(instance.continent)
          registerApiService(instance.continent, apiInstance, instance)

          if (hasSocketService(instance.continent)) {
            const socketService = getSocketService(instance.continent)
            registerSocketService(instance.continent, socketService, instance)
          }

          logger.info(
            `Successfully initialized client for Continent ${instance.continent}`
          )
        } else {
          logger.error(
            `Failed to initialize client for Continent ${instance.continent}: ${result.error?.message}`
          )
        }
      } catch (error) {
        logger.error(
          `Error initializing client for Continent ${instance.continent}:`,
          error
        )
      }
    }

    if (!loginSuccessful) {
      throw new Error('Failed to initialize any client')
    }

    logger.info('Loading commands...')
    await commandManager.loadCommands()

    await deployCommands(botConfig)

    const client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
      ]
    })

    client.on('interactionCreate', async (interaction: Interaction) => {
      if (
        !interaction.isCommand() &&
        !interaction.isButton() &&
        !interaction.isStringSelectMenu()
      )
        return

      const instanceConfig = instances.find(
        instance => instance.guild_id === interaction.guildId
      )
      if (!instanceConfig) {
        logger.warn(`No instance config found for guild ${interaction.guildId}`)
        return
      }

      // Use ServiceRegistry for service retrieval with fallback to legacy methods
      let apiInstance, socketService
      try {
        apiInstance = getApiService(instanceConfig.continent)
        socketService = hasRegistrySocketService(instanceConfig.continent)
          ? getRegistrySocketService(instanceConfig.continent)
          : null
      } catch {
        // Fallback to legacy service retrieval
        apiInstance = getApiInstance(instanceConfig.continent)
        socketService = hasSocketService(instanceConfig.continent)
          ? getSocketService(instanceConfig.continent)
          : null
      }

      await handleInteraction(
        interaction,
        instanceConfig,
        apiInstance,
        titleToggleMap[instanceConfig.continent],
        roleStatusMap[instanceConfig.continent],
        socketService
      )
    })

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      logger.info('Received SIGINT. Closing all socket connections...')
      closeAllSocketServices()
      process.exit(0)
    })

    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM. Closing all socket connections...')
      closeAllSocketServices()
      process.exit(0)
    })

    await client.login(bot_token)
    logger.info('Bot is now online!')
  } catch (error) {
    logger.error('Fatal error:', { error })
    process.exit(1)
  }
}

main()
