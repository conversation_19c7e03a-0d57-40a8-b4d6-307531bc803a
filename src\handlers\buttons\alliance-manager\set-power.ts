/* eslint-disable @typescript-eslint/no-unused-vars */
import type { ButtonInteraction } from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types'

import type { InstanceConfig } from '../../../interfaces/config'
import type { ApiService } from '../../../services/api/api-service'
import { logger } from '../../../utils/logging/logger'

export async function handleSetMinPower(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap
): Promise<void> {
  try {
    await interaction.reply({
      content:
        'Please use the `/set_power <value>` command to change the minimum power requirement.',
      ephemeral: true
    })
  } catch (error) {
    logger.error('Error handling set min power button:', error)
    await interaction.reply({
      content: 'An error occurred. Please try again.',
      ephemeral: true
    })
  }
}
