import type {
 Client, TextChannel, EmbedBuilder 
} from 'discord.js'
import type { InstanceConfig } from 'interfaces/config'
import { BotError } from 'utils/common/error.js'

import { MessageUtils } from '../../utils/discord/message-utils.js'
import { getTimeUntil } from '../../utils/time/time-utils.js'
import { getApiInstance } from '../api/api-instance'
import { getCvcWorldId } from '../cvc/cvc-service.js'

import type {
 ProcessedShrine, ShrineObject 
} from './types.js'

export async function fetchShrineData(
  instanceConfig: InstanceConfig
): Promise<ShrineObject[]> {
  const api = getApiInstance(instanceConfig.continent)
  const { cvcWorldId } = await getCvcWorldId(instanceConfig)

  try {
    const response = await api.post<{
      result: boolean
      objects: ShrineObject[]
    }>('/field/worldmap/objects', { worldId: cvcWorldId })

    if (!response.result || !Array.isArray(response.objects)) {
      throw new BotError('Error fetching shrine data', 'SHRINE_DATA_ERROR')
    }
    return response.objects
  } catch (error) {
    throw new BotError('Failed to fetch shrine data', 'SHRINE_FETCH_ERROR')
  }
}

export function processShrine(shrineData: ShrineObject[]): ProcessedShrine[] {
  const now = new Date()
  return shrineData
    .filter(shrine => shrine.open && shrine.open.started)
    .map(shrine => ({
      type: shrine.type,
      coords: shrine.loc.slice(1),
      startTime: new Date(shrine.open.started),
      timeRemaining: Math.floor(
        (new Date(shrine.open.started).getTime() - now.getTime()) / 1000 / 60
      ),
      controllingContinent: shrine.worldId || 'None'
    }))
    .sort((a, b) => a.timeRemaining - b.timeRemaining)
}

export function createShrineEmbed(
  processedData: ProcessedShrine[]
): EmbedBuilder {
  if (processedData.length === 0) {
    return MessageUtils.createEmbed(
      'CVC Shrine Timers',
      'No upcoming shrine events at this time.'
    )
  }

  const fields = processedData.map(shrine => ({
    name: `Shrine Type ${shrine.type}`,
    value: `Coords: (${shrine.coords.join(', ')})
Time Until: ${getTimeUntil(shrine.startTime)}
Controlling Continent: ${shrine.controllingContinent}`,
    inline: true
  }))

  return MessageUtils.createEmbed('CVC Shrine Timers', '', fields)
}

export async function updateShrineEmbed(
  instanceConfig: InstanceConfig,
  client: Client,
  channelId: string,
  messageId: string | null
): Promise<void> {
  try {
    const shrineData = await fetchShrineData(instanceConfig)
    const processedData = processShrine(shrineData)
    const embed = createShrineEmbed(processedData)

    const channel = (await client.channels.fetch(channelId)) as TextChannel
    if (!channel) {
      throw new BotError('Shrine channel not found', 'CHANNEL_NOT_FOUND')
    }

    await MessageUtils.updateOrSendMessage(
      channel,
      { embeds: [embed] },
      messageId
    )
  } catch (error) {
    throw new BotError(
      'Failed to update shrine embed',
      'SHRINE_EMBED_UPDATE_ERROR'
    )
  }
}
