/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON><PERSON><PERSON> } from 'buffer'

import pako from 'pako'

export function decodeRegionHash(regionHash: string) {
  const decodedString = Buffer.from(regionHash, 'base64').toString('utf-8')
  const self = decodedString.split('-')[1]
  if (!self) throw new Error('Failed to decode regionHash')
  return self
}

export function b64xorEnc(key: string, data: any) {
  //logger.info('Encoding data with XOR');
  const dataString = JSON.stringify(data).replace(/\\/g, '')
  const encodedData = [...dataString]
    .map((c, i) =>
      String.fromCharCode(c.charCodeAt(0) ^ key.charCodeAt(i % key.length))
    )
    .join('')
  const encodedString = btoa(encodedData).replace(/=+$/, '')
  return encodedString
}

export function b64xorDec(key: string, data: any) {
  //logger.info('Decoding data with XOR');
  const decoded = atob(data)
  const decodedData = Array.from(decoded)
    .map((c, i) =>
      String.fromCharCode(c.charCodeAt(0) ^ key.charCodeAt(i % key.length))
    )
    .join('')
  return decodedData
}

export function decodeResponse(key: string, data: unknown) {
  return JSON.parse(b64xorDec(decodeRegionHash(key), data))
}

export function parseData(data: any, key: string | null) {
  if (typeof data === 'string') {
    if (data.indexOf('ey') === 0) {
      data = atob(data)
    }
    if (data.startsWith('V')) {
      if (!key) {
        throw new Error('Failed to parse data. No key provided for decoding')
      }
      data = b64xorDec(key, data)
    }
    if (data.startsWith('{')) {
      data = JSON.parse(data)
    }
    if (
      typeof data === 'object' &&
      (data.isPacked || data.isPackCompressed || data.compType === 3)
    ) {
      return parseData(data, key)
    }
    return data
  }
  if (typeof data === 'object') {
    if (
      (data.isPacked || data.isPackCompressed || data.compType === 3) &&
      (data.packs || data.payload)
    ) {
      data = pako.inflate(data.packs || data.payload, { to: 'string' })
      return parseData(data, key)
    }
    return data.packs || data.payload || data
  }
}
