# Discord Command Execution Flow

## Overview
The command execution flow handles Discord slash command interactions from user input through command processing to response delivery.

## Entry Point
- **File**: `src/handlers/interactions/InteractionHandler.ts`
- **Function**: `handleInteraction()`
- **Trigger**: Discord `interactionCreate` event

## Flow Sequence

### 1. Interaction Reception
```typescript
client.on('interactionCreate', async (interaction) => {
  await handleInteraction(interaction, titleToggleMap, roleStatusMap)
})
```

**Initial Processing:**
- Receives interaction from Discord API
- Determines interaction type (command, button, select menu)
- Routes to appropriate handler

### 2. Command Type Detection
```typescript
// src/handlers/interactions/InteractionHandler.ts
if (interaction.isChatInputCommand()) {
  await handleCommand(interaction, instanceConfig, apiInstance, ...)
}
```

### 3. Instance Resolution
```typescript
const instanceConfig = getInstanceConfig(interaction.guildId)
const apiInstance = getApiInstance(instanceConfig.continent)
```

**Process:**
- Maps Discord guild ID to bot instance configuration
- Retrieves continent-specific API service
- Validates instance exists and is configured

### 4. Command Handler Resolution
```typescript
// src/handlers/interactions/CommandInteractions.ts
const command = commandManager.getCommand(commandName)
```

**Command Manager Process:**
- Looks up command by name in registered commands collection
- Returns command definition with handler class
- **Error**: Returns 404 if command not found

### 5. Context Creation
```typescript
// src/interfaces/context.ts
const context = createCommandContext(
  interaction,
  instanceConfig,
  apiInstance,
  titleToggleMap,
  roleStatusMap,
  socketService
)
```

**Context Object Contains:**
- Discord interaction object
- Instance configuration
- API service instance
- Title toggle state map
- Role status map
- Socket service (if available)

### 6. Handler Instantiation
```typescript
// Dual constructor pattern for backward compatibility
let handler
try {
  handler = new command.CommandHandlerClass(context)
} catch {
  // Fallback to legacy constructor
  handler = new command.CommandHandlerClass(
    interaction, instanceConfig, apiInstance, ...
  )
}
```

### 7. Command Execution
```typescript
// src/handlers/commands/command-handler.ts
await handler.execute()
```

## Base Command Handler Architecture

### Constructor Pattern
```typescript
export abstract class BaseCommandHandler {
  constructor(context: CommandContext)
  constructor(interaction, instanceConfig, apiInstance, ...)
}
```

**Features:**
- Dual constructor for migration compatibility
- Context-based parameter consolidation
- Convenient getter methods for backward compatibility

### Common Handler Methods

#### Token Management
```typescript
async ensureToken(): Promise<void> {
  if (!this.instanceConfig.token) {
    await this.refreshToken()
  }
}
```

#### Role Validation
```typescript
async validateRole(requiredRole: string): Promise<boolean> {
  return CommandUtils.hasRole(this.interaction, requiredRole)
}
```

#### Channel Verification
```typescript
async verifyChannel(): Promise<boolean> {
  return verifyCommandChannel(this.interaction, this.instanceConfig)
}
```

## Command Types and Patterns

### 1. Simple Commands
**Example**: `/verify`
- Direct API call
- Immediate response
- No complex state management

### 2. Panel Creation Commands
**Example**: `/create_title_panel`
- Creates Discord embed with buttons
- Stores panel state
- Sets up interaction handlers

### 3. Data Retrieval Commands
**Example**: `/player_data`
- Fetches data from game API
- Formats response
- Handles pagination if needed

### 4. Administrative Commands
**Example**: `/set_power`
- Requires admin role validation
- Modifies bot configuration
- Updates monitoring services

## Error Handling

### Command-Level Errors
```typescript
// src/utils/discord/interaction-error-handler.ts
await InteractionErrorHandler.handleCommandError(
  interaction,
  error,
  commandName
)
```

**Error Types:**
- **Command Not Found**: User-friendly message
- **Permission Denied**: Role requirement notification
- **API Errors**: Retry suggestions or status information
- **Validation Errors**: Input correction guidance

### Response Management
```typescript
if (!interaction.replied && !interaction.deferred) {
  await interaction.reply({ content: errorMessage, ephemeral: true })
} else if (!interaction.replied) {
  await interaction.editReply({ content: errorMessage })
}
```

## Command Registration System

### Static Registration
```typescript
// src/utils/discord/command-manager.ts
const commandList = [
  verifyCommand,
  titlePanelCommand,
  requestTitleCommand,
  // ... all commands
]
```

### Command Structure
```typescript
export default {
  data: {
    name: 'command_name',
    description: 'Command description'
  },
  CommandHandlerClass: HandlerClass
}
```

## Performance Optimizations

### Concurrent Request Handling
- Multiple commands can execute simultaneously
- Per-instance resource isolation
- Connection pooling for API requests

### Caching Strategies
- Command definitions cached in memory
- Instance configurations cached
- API responses cached where appropriate

### Rate Limiting
- Discord interaction response timeouts handled
- API rate limiting with retry logic
- Queue management for high-volume commands

## Security Considerations

### Input Validation
- All user inputs sanitized
- Command parameters validated
- SQL injection prevention (where applicable)

### Permission Checks
- Role-based access control
- Channel restrictions
- Guild-specific command availability

### Token Security
- Tokens stored securely in memory
- Automatic token refresh
- No token logging

## Monitoring and Logging

### Command Metrics
- Execution time tracking
- Success/failure rates
- User interaction patterns

### Debug Information
- Full command context logging
- Error stack traces
- Performance bottleneck identification

### Audit Trail
- Administrative command usage
- Permission changes
- Configuration modifications

## Integration Points

### External APIs
- Game server API calls
- Authentication services
- Data retrieval services

### Internal Services
- Socket service for real-time data
- Monitor services for background tasks
- Storage services for persistent data

### Discord Features
- Embed creation and updates
- Button and select menu interactions
- Message management and cleanup
