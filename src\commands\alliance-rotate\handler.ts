import { ButtonStyle } from 'discord.js'

import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import { setSettings } from '../../utils/config/config-utils.js'
import { createRotatePanel } from '../../services/monitor/rotation/utils/rotate-panel-embed.js'
import { logger } from '../../utils/logging/logger.js'

export class HandleRotatePanel extends BaseCommandHandler {
  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return

    await this.interaction.deferReply({ ephemeral: true })

    await this.ensureToken()

    try {
      const message = await this.interaction.channel?.send({
        ...(await createRotatePanel(this.instanceConfig)),
        components: [this.createRotateButtons()]
      })

      if (message) {
        this.instanceConfig.rotate = {
          ...this.instanceConfig.rotate,
          messageId: message.id,
          channelId: message.channel.id
        }

        await setSettings(this.instanceConfig, 'rotate.messageId', message.id)
        await setSettings(
          this.instanceConfig,
          'rotate.channelId',
          message.channel.id
        )

        await this.interaction.editReply({
          content: 'Rotate panel has been created.'
        })
      }
    } catch (error) {
      logger.error('Error creating rotate panel:', error)
      await this.interaction.editReply({
        content: 'Failed to create rotate panel.'
      })
    }
  }

  private createRotateButtons() {
    return this.messageUtils.createActionRow(
      this.messageUtils.createButton(
        'rotate-enable',
        'Enable Rotation',
        ButtonStyle.Success
      ),
      this.messageUtils.createButton(
        'rotate-disable',
        'Disable Rotation',
        ButtonStyle.Danger
      ),
      this.messageUtils.createButton(
        'rotate-set-max-kick',
        'Set Max Kick',
        ButtonStyle.Secondary
      )
    )
  }
}

export default {
  data: {
    name: 'create_rotate_panel',
    description: 'Create the rotate panel'
  },
  CommandHandlerClass: HandleRotatePanel
}
