import { InstanceConfig } from '../../interfaces/config'

export interface ApiRequestOptions {
  method: 'GET' | 'POST'
  url: string
  headers?: Record<string, string>
  data?: unknown
  params?: Record<string, string | number>
  timeout?: number
  instanceConfig?: InstanceConfig
}

export interface ApiResponse<T> {
  result: boolean
  data: T
  err?: {
    code: string
    message: string
  }
}

export type Params = {
  token: string
  xorKey: string
  instanceConfig: InstanceConfig
  onError?: (error: Error) => void
}
