export interface RallyObject {
  _id: string
  loc: {
    continent: number
    x: number
    y: number
  }
  level: number
  code: number
  param?: {
    value?: number
  }
  state: number
  expired?: string
}

export interface KingdomObject {
  _id: string
  loc: {
    continent: number
    x: number
    y: number
  }
  level: number
  code: number
  occupied?: {
    id: string
    started: string
    skin?: number
    skingrade?: number
    name: string
    worldId: number
    titleCode?: number
    allianceId: string
    allianceTag: string
  }
}

export interface FieldObjectV4 {
  isPackCompressed: boolean
  packs: number[]
}

export interface FieldObjectV4Decoded {
  objects: KingdomObject[]
}

export const OBJECT_CODES = {
  20300101: 'kingdom',
  20200201: 'deathkar',
  20700505: 'magdar',
  20700506: 'spartoi'
} as const

export const FILTER_OCCUPIED_CODES = [20200201, 20700505, 20700506]
