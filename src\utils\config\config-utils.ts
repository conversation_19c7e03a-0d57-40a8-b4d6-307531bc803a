import type { InstanceConfig } from '../../interfaces/config'
import { logger } from '../logging/logger'

type Primitive = string | number | boolean | null | undefined

type NestedObject<T> = {
  [K in keyof T]: T[K] extends Primitive
    ? T[K]
    : T[K] extends Array<infer U>
      ? Array<NestedObject<U>>
      : NestedObject<T[K]>
}

type NestedKeyOf<T> = {
  [K in keyof T & (string | number)]: T[K] extends Primitive
    ? `${K}`
    : `${K}` | `${K}.${NestedKeyOf<T[K]>}`
}[keyof T & (string | number)]

export function setSettings<T extends InstanceConfig>(
  instanceConfig: T,
  path: NestedKeyOf<T>,
  value: unknown
): void {
  const keys = (path as string).split('.')
  let current: NestedObject<T> = instanceConfig as NestedObject<T>

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i] as keyof typeof current
    if (typeof current[key] !== 'object') {
      current[key] = {} as NestedObject<T>[typeof key]
    }
    current = current[key] as NestedObject<T>
  }

  const lastKey = keys[keys.length - 1] as keyof typeof current
  current[lastKey] = value as NestedObject<T>[typeof lastKey]

  logger.info(
    `Updated instanceConfig setting: ${path} = ${JSON.stringify(value)}`
  )
}

export function getNestedProperty<T extends InstanceConfig>(
  obj: T,
  path: NestedKeyOf<T>
): unknown {
  const keys = (path as string).split('.')
  let current: NestedObject<T> = obj as NestedObject<T>

  for (const key of keys) {
    if (current === undefined || current === null) {
      return undefined
    }
    current = current[key as keyof typeof current] as NestedObject<T>
  }

  return current
}
