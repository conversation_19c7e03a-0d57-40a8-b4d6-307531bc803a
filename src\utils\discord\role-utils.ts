import {
 GuildMember, Interaction, InteractionReplyOptions 
} from 'discord.js'

export async function validateRoleOrReply(
  interaction: Interaction,
  requiredRole: string,
  customReplyOptions?: InteractionReplyOptions
): Promise<boolean> {
  if (!interaction.guild || !interaction.member) {
    if (interaction.isRepliable()) {
      await interaction.reply({
        content: 'This command can only be used in a server.',
        ephemeral: true,
        ...customReplyOptions
      })
    }
    return false
  }

  const member = interaction.member as GuildMember
  const hasRole = member.roles.cache.some(
    role => role.name === requiredRole || role.id === requiredRole
  )

  if (!hasRole && interaction.isRepliable()) {
    await interaction.reply({
      content: `You need the ${requiredRole} role to use this command.`,
      ephemeral: true,
      ...customReplyOptions
    })
    return false
  }

  return hasRole
}
