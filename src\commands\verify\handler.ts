import { <PERSON><PERSON>ommandHandler } from '../../handlers/commands/command-handler.js'
import { addRoleToMember } from '../../services/role/role-service.js'
import { verifyUser } from '../../services/verify/verify-service.js'

class HandleVerify extends BaseCommandHandler {
  async execute(): Promise<void> {
    if (!(await this.verifyCommandChannel('verify'))) {
      return
    }

    await this.interaction.deferReply({ ephemeral: false })

    try {
      const kingdomId = this.interaction.options.getString('uid', true)

      await this.ensureToken()

      const playerProfile = await verifyUser(
        this.instanceConfig,
        kingdomId,
        this.interaction.user.id
      )

      await this.interaction.editReply(playerProfile.message)

      if (playerProfile.isVerified) {
        const member = await this.interaction.guild?.members.fetch(
          this.interaction.user.id
        )
        if (!member) {
          throw new Error('Failed to fetch member')
        }

        const role = this.interaction.guild?.roles.cache.find(
          role => role.name === this.instanceConfig.verify.roleName
        )
        if (!role) {
          throw new Error('Verification role not found')
        }

        if (member.roles.cache.has(role.id)) {
          this.logger.info(`Member already has verification role`, {
            memberName: member.displayName,
            roleName: role.name
          })
          await this.interaction.followUp({
            content: `You already have the '${role.name}' role.`,
            ephemeral: false
          })
        } else {
          await addRoleToMember(member, role)
          await this.interaction.followUp({
            content: `Role '${role.name}' has been added to ${member.displayName}.`,
            ephemeral: false
          })
          this.logger.info(`Verification role added to member`, {
            memberName: member.displayName,
            roleName: role.name
          })
        }
      }
    } catch (error) {
      await this.handleError(error, 'Failed to verify and add role.')
    }
  }
}

export default {
  data: {
    name: 'verify',
    description: 'Verify your account and receive a role',
    options: [
      {
        name: 'uid',
        description: 'Your kingdom ID',
        type: 3,
        required: true
      }
    ]
  },
  CommandHandlerClass: HandleVerify
}
