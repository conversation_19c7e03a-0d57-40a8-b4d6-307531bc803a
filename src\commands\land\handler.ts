import fs from 'fs/promises'
import path from 'path'

import { createObjectCsvWriter } from 'csv-writer'
import {
 format, subDays 
} from 'date-fns'
import {
 type ChatInputCommandInteraction, EmbedBuilder 
} from 'discord.js'
import { BaseCommandHandler } from 'handlers/commands/command-handler.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'
import { landInfo } from '../../services/land/land-service.js'
import { BotError } from '../../utils/common/error.js'

export class HandleLand extends BaseCommandHandler {
  constructor(
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap: TitleToggleMap,
    roleStatusMap: RoleStatusMap
  ) {
    super(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap
    )
  }

  async execute(): Promise<void> {
    const landId = this.interaction.options.getString('landid', true)
    const startDateInput = this.interaction.options.getString('start_date')
    const endDateInput = this.interaction.options.getString('end_date')

    const today = new Date()
    const startDate = startDateInput || format(subDays(today, 7), 'MM/dd/yyyy')
    const endDate = endDateInput || format(today, 'MM/dd/yyyy')

    await this.deferReply(false)

    try {
      const results = await landInfo(startDate, endDate, landId)

      const contributions = results[landId]
      if (!contributions || contributions.length === 0) {
        await this.interaction.editReply(
          `No contribution data found for Land ID: ${landId} between ${startDate} and ${endDate}.`
        )
        return
      }

      const totalPoints = contributions.reduce(
        (sum, contribution) => sum + contribution.total,
        0
      )
      const top10Contributors = contributions
        .slice(0, 10)
        .map(
          (contribution, index) =>
            `${index + 1}. ${contribution.name}: ${Math.round(contribution.total).toLocaleString()} points`
        )
        .join('\n')

      const summaryEmbed = new EmbedBuilder()
        .setColor('#f1c40f')
        .setTitle(`🗺️ Land Data for Land ID: ${landId}`)
        .setDescription(
          `🏗️ Total Dev Points: ${Math.round(totalPoints).toLocaleString()}`
        )
        .addFields(
          {
            name: '🏆 Top 10 Contributors',
            value: top10Contributors || 'No contributors found'
          },
          { name: '📅 Start Date', value: startDate, inline: true },
          { name: '🏁 End Date', value: endDate, inline: true },
          {
            name: '👥 Total Contributors',
            value: contributions.length.toString(),
            inline: true
          }
        )
        .setTimestamp()

      const csvFileName = `LandId_${landId}.csv`
      const csvFilePath = path.resolve(process.cwd(), 'temp', csvFileName)
      await fs.mkdir(path.resolve(process.cwd(), 'temp'), { recursive: true })

      const csvWriter = createObjectCsvWriter({
        path: csvFilePath,
        header: [
          { id: 'name', title: 'Name' },
          { id: 'total', title: 'Total Dev Points' },
          { id: 'kingdomId', title: 'Kingdom ID' },
          { id: 'continent', title: 'Continent' }
        ]
      })

      await csvWriter.writeRecords(
        contributions.map(contribution => ({
          name: contribution.name,
          total: Math.round(contribution.total),
          kingdomId: contribution.kingdomId,
          continent: contribution.continent
        }))
      )

      await this.interaction.editReply({
        embeds: [summaryEmbed],
        files: [csvFilePath]
      })

      await fs.unlink(csvFilePath)
      this.logger.info(`Successfully processed and deleted: ${csvFileName}`)
    } catch (error) {
      if (error instanceof BotError && error.code === 'INVALID_DATE_FORMAT') {
        await this.interaction.editReply(
          'Please use the format MM/dd/yyyy for dates.'
        )
        return
      }

      await this.handleError(
        error,
        'An error occurred while processing your request. Please try again later.'
      )
    }
  }
}

export default {
  data: {
    name: 'land',
    description: 'Get land contribution data',
    options: [
      {
        name: 'landid',
        description: 'The ID of the land',
        type: 3, // STRING
        required: true
      },
      {
        name: 'start_date',
        description: 'Start date (MM/dd/yyyy)',
        type: 3, // STRING
        required: false
      },
      {
        name: 'end_date',
        description: 'End date (MM/dd/yyyy)',
        type: 3, // STRING
        required: false
      }
    ]
  },
  CommandHandlerClass: HandleLand
}
