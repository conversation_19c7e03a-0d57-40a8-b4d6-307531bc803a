import { DAY } from './constants.js'

export function formatDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60

  if (hours > 0) {
    return `${hours}h ${remainingMinutes}m`
  }

  return `${remainingMinutes}m`
}

export function getTimeUntil(targetDate: Date): string {
  const now = new Date()
  const diff = targetDate.getTime() - now.getTime()

  if (diff <= 0) {
    return 'Now'
  }

  const minutes = Math.floor(diff / (1000 * 60))
  return formatDuration(minutes)
}

export function parseDate(dateString: string): Date {
  const date = new Date(dateString)
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date string')
  }
  return date
}

export function getNextOccurrence(hour: number, minute: number): Date {
  const now = new Date()
  const next = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
    hour,
    minute
  )

  if (next <= now) {
    next.setDate(next.getDate() + 1)
  }

  return next
}

export async function wait(delay = 1000) {
  if (!delay) return
  return new Promise(function (resolve) {
    setTimeout(resolve, delay)
  })
}

export function getRandomDelay(min: number, max: number) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

const now = Date.now()
export const nextDecade = now + 3650 * DAY

export function getNextUTCReset(): number {
  const currentDate = new Date()
  const nextDate = new Date(currentDate)
  nextDate.setUTCHours(0, 0, 0, 0)
  if (currentDate >= nextDate) {
    nextDate.setUTCDate(nextDate.getUTCDate() + 1)
  }
  return nextDate.getTime()
}
