export interface PlayerProfile {
  name: string
  id: string
  continent: number
  power?: number
  kill?: number | string
  death?: number | string
  gathering?: number | string
  individualRank?: number | null
  continentRank?: number | null
  individualPoints?: number | null
}

export interface UserData {
  [discordId: string]: PlayerProfile[]
}

export interface PlayerProfileResponse {
  profile: {
    worldId: number
    name: string
    _id: string
  }
}

export interface PlayerHistoryResponse {
  history: {
    power: {
      total: number
    }
    stats: {
      battle: {
        kill: number
        death: number
      }
      economy: {
        gathering: number
      }
    }
  }
}

export interface KingdomData {
  kingdom: {
    _id: string
    name: string
    loc: number[]
    fieldObjectId: string
  }
  networks: {
    api: string
    chats: string[]
    fields: string[]
    kingdoms: string[]
  }
}
