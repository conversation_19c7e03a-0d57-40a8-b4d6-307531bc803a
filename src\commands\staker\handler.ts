import type { ChatInputCommandInteraction } from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types'

import { BaseCommandHandler } from '../../handlers/commands/command-handler'
import type { InstanceConfig } from '../../interfaces/config'
import type { ApiService } from '../../services/api/api-service'
import { StakerService } from '../../services/staker/service'

export class HandleStaker extends BaseCommandHandler {
  stakerService: StakerService

  constructor(
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap: TitleToggleMap,
    roleStatusMap: RoleStatusMap
  ) {
    super(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap
    )
    this.stakerService = new StakerService(instanceConfig)
  }

  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) {
      await this.interaction.reply({
        content: "You don't have permission to use this command.",
        ephemeral: true
      })
      return
    }

    await this.interaction.deferReply({ ephemeral: false })

    try {
      await this.stakerService.updateStakerInformation(this.interaction.client)
      await this.interaction.editReply(
        'Staker information has been updated and monitoring has started.'
      )
    } catch (error) {
      this.logger.error('Error in HandleStaker execute:', error)
      await this.interaction.editReply(
        `An error occurred while updating staker information: ${error}. Please check the logs for details.`
      )
    }
  }
}

export default {
  data: {
    name: 'staker',
    description: 'Fetch staker information and start monitoring'
  },
  CommandHandlerClass: HandleStaker
}
