import type { ButtonInteraction } from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import type { InstanceConfig } from '../../../interfaces/config.js'
import { expireRole } from '../../../services/title/service.js'
import { updateTitlePanelEmbed } from '../../../services/title/utils/title-panel-embed.js'
import { logger } from '../../../utils/logging/logger.js'

export async function handleExpireRole(
  interaction: ButtonInteraction,
  role: string,
  roleStatusMap: RoleStatusMap,
  titleToggleMap: TitleToggleMap,
  instanceConfig: InstanceConfig
): Promise<void> {
  try {
    const expired = expireRole(role, instanceConfig.continent, roleStatusMap)

    if (expired) {
      await interaction.reply({
        content: `The ${role} role has been expired.`,
        ephemeral: true
      })
      await updateTitlePanelEmbed(
        interaction.client,
        instanceConfig,
        roleStatusMap,
        titleToggleMap
      )
    } else {
      await interaction.reply({
        content: 'No active role found to expire.',
        ephemeral: true
      })
    }
  } catch (error) {
    logger.error('Error handling expire role:', error)
    await interaction.reply({
      content: 'An error occurred while expiring the role.',
      ephemeral: true
    })
  }
}
