import tseslint from 'typescript-eslint'
import eslintPluginImport from 'eslint-plugin-import'

export default tseslint.config({
  extends: [...tseslint.configs.recommended],
  ignores: [
    '**/dist/**',
    'dist/**/*',
    'config/**/*',
    '*.config.js',
    '*.config.ts',
    '*.json',
    '.eslintrc.*'
  ],
  files: ['src/**/*.ts'],
  languageOptions: {
    parser: tseslint.parser,
    parserOptions: {
      project: './tsconfig.json'
    }
  },
  plugins: {
    import: eslintPluginImport
  },
  rules: {
    '@typescript-eslint/no-explicit-any': ['warn', { fixToUnknown: true }],
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/no-var-requires': 'warn',
    'object-curly-newline': [
      'warn',
      {
        ImportDeclaration: { multiline: true, minProperties: 2 },
        ExportDeclaration: { multiline: true, minProperties: 2 }
      }
    ],
    'object-property-newline': ['warn', { allowAllPropertiesOnSameLine: true }],
    'import/order': [
      'warn',
      {
        groups: [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index'
        ],
        'newlines-between': 'always',
        alphabetize: { order: 'asc', caseInsensitive: true }
      }
    ],
  }
})
