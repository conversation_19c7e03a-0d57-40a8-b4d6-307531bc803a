import { getRandomDelay, wait } from '../../../utils/timer'
import { zoneOnSpiral } from '../../../utils/zone-util'

export class SearchStrategy {
  searchDistance: number
  spiralSteps: number

  constructor(searchDistance: number) {
    this.searchDistance = searchDistance
    this.spiralSteps = Math.floor(this.searchDistance / 15)
  }

  getNextZones(
    client,
    x: number,
    y: number
  ): { center: number; zones: number[] } {
    client.search.autoSearchStep %= this.spiralSteps
    return zoneOnSpiral(x, y, client.search.autoSearchStep ?? 0)
  }

  getHomeZones(x: number, y: number): { center: number; zones: number[] } {
    return zoneOnSpiral(x, y, 0)
  }

  async handleDelays(): Promise<void> {
    await wait(getRandomDelay(2000, 4000))
  }

  incrementStep(client): void {
    client.search.autoSearchStep++
  }

  isComplete(client): boolean {
    return client.search.autoSearchStep >= this.spiralSteps
  }

  // needs to be modified
  shouldTake<PERSON>reak(client): boolean {
    return client.search.autoSearchStep % 7 === 0
  }
}
