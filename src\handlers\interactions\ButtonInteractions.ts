import type { ButtonInteraction } from 'discord.js'
import { handleDisableManager } from 'handlers/buttons/alliance-manager/disable-manager.js'
import { handleEnableManager } from 'handlers/buttons/alliance-manager/enable-manager.js'
import { handleSetMinPower } from 'handlers/buttons/alliance-manager/set-power.js'
import { handleDisableRotation } from 'handlers/buttons/alliance-rotate/disable-rotation.js'
import { handleEnableRotation } from 'handlers/buttons/alliance-rotate/enable-rotation.js'
import { handleSetMaxKick } from 'handlers/buttons/alliance-rotate/set-max-kick.js'
import { handleEjectDisable, handleEjectEnable } from 'handlers/buttons/eject/select-eject-status.js'
import { handleActivateSkill } from 'handlers/buttons/skills/activate-skill.js'
import { handleRefreshSkills } from 'handlers/buttons/skills/refresh-skills.js'
import { SocketService } from 'services/socket/service.js'
import type {
 RoleStatusMap, TitleToggleMap
} from 'services/title/types.js'

import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'
import { InteractionErrorHandler } from '../../utils/discord/interaction-error-handler.js'
import { logger } from '../../utils/logging/logger.js'
import { handleAllianceRankPagination } from '../buttons/alliance-rank-pagination.js'
import { handleAddKingdom } from '../buttons/title/add-kingdom.js'
import { handleExpireRole } from '../buttons/title/expire-role.js'
import {
  handleSelectAlchemist,
  handleSelectArchitect
} from '../buttons/title/select-role.js'

export type ButtonHandlerFunction = (
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap,
  socketService?: SocketService
) => Promise<void>

const buttonHandlers: Map<string, ButtonHandlerFunction> = new Map([
  ['alliance-prev', handleAllianceRankPagination as ButtonHandlerFunction],
  ['alliance-next', handleAllianceRankPagination as ButtonHandlerFunction],
  ['enable-manager', handleEnableManager as ButtonHandlerFunction],
  ['disable-manager', handleDisableManager as ButtonHandlerFunction],
  ['global-skill-101', handleActivateSkill as ButtonHandlerFunction],
  ['global-skill-102', handleActivateSkill as ButtonHandlerFunction],
  ['global-skill-103', handleActivateSkill as ButtonHandlerFunction],
  ['global-skill-104', handleActivateSkill as ButtonHandlerFunction],
  ['refresh-skills', handleRefreshSkills as ButtonHandlerFunction],
  ['rotate-enable', handleEnableRotation as ButtonHandlerFunction],
  ['rotate-disable', handleDisableRotation as ButtonHandlerFunction],
  ['rotate-set-max-kick', handleSetMaxKick as ButtonHandlerFunction],
  ['set-power', handleSetMinPower as ButtonHandlerFunction],
  ['eject-on', handleEjectEnable as ButtonHandlerFunction],
  ['eject-off', handleEjectDisable as ButtonHandlerFunction],
  [
    'expire-role',
    async (
      interaction: ButtonInteraction,
      instanceConfig: InstanceConfig,
      apiInstance: ApiService,
      titleToggleMap: TitleToggleMap,
      roleStatusMap: RoleStatusMap
    ) => {
      const currentRole = roleStatusMap[instanceConfig.continent]?.roleName
      if (!currentRole) {
        await interaction.reply({
          content: 'No role is currently assigned.',
          ephemeral: true
        })
        return
      }
      await handleExpireRole(
        interaction,
        currentRole,
        roleStatusMap,
        titleToggleMap,
        instanceConfig
      )
    }
  ],
  ['title-alchemist', handleSelectAlchemist],
  ['title-architect', handleSelectArchitect],
  ['title-add-kingdom', handleAddKingdom]
])

export async function handleButton(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap,
  socketService?: SocketService
): Promise<void> {
  const handler = buttonHandlers.get(interaction.customId)

  if (!handler) {
    logger.warn(`Unknown button action: ${interaction.customId}`)
    await InteractionErrorHandler.handleError(
      interaction,
      new Error(`Unknown button action: ${interaction.customId}`),
      { customMessage: 'Unknown button action' }
    )
    return
  }

  try {
    await handler(
      interaction,
      instanceConfig,
      apiInstance,
      titleToggleMap,
      roleStatusMap,
      socketService
    )
  } catch (error) {
    await InteractionErrorHandler.handleButtonError(
      interaction,
      error,
      interaction.customId
    )
  }
}
