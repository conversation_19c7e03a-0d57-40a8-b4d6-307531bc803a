import { BaseCommandHandler } from '../../handlers/commands/command-handler.js'
import { setSettings } from '../../utils/config/config-utils.js'
import { updateRotatePanel } from '../../services/monitor/rotation/utils/rotate-panel-embed.js'
import { logger } from '../../utils/logging/logger.js'

export class HandleSetMaxKick extends BaseCommandHandler {
  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return

    const newMaxKick = this.interaction.options.getInteger('value', true)

    if (newMaxKick < 1) {
      await this.interaction.reply({
        content: 'Max kick value must be at least 1.',
        ephemeral: true
      })
      return
    }

    try {
      this.instanceConfig.rotate!.maxKick = newMaxKick
      await setSettings(this.instanceConfig, 'rotate.maxKick', newMaxKick)

      await updateRotatePanel(this.interaction.client, this.instanceConfig)

      await this.interaction.reply({
        content: `Maximum kick value has been set to ${newMaxKick}.`,
        ephemeral: true
      })
      logger.info(
        `Max kick set to ${newMaxKick} for continent ${this.instanceConfig.continent}`
      )
    } catch (error) {
      logger.error('Error setting max kick:', error)
      await this.interaction.reply({
        content: 'An error occurred while setting the max kick value.',
        ephemeral: true
      })
    }
  }
}

export default {
  data: {
    name: 'set_max_kick',
    description: 'Set the maximum number of kicks for alliance rotation',
    options: [
      {
        name: 'value',
        type: 4, // INTEGER type
        description: 'The maximum number of kicks',
        required: true
      }
    ]
  },
  CommandHandlerClass: HandleSetMaxKick
}
