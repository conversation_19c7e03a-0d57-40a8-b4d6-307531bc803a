/* eslint-disable @typescript-eslint/no-unused-vars */
import type { ButtonInteraction } from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types'

import type { InstanceConfig } from '../../../interfaces/config'
import type { ApiService } from '../../../services/api/api-service'
import { logger } from '../../../utils/logging/logger'

export async function handleSetMaxKick(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap
): Promise<void> {
  try {
    await interaction.reply({
      content:
        'Please use the `/set_max_kick <value>` command to change the maximum number of kicks.',
      ephemeral: true
    })
  } catch (error) {
    logger.error('Error handling set max kick button:', error)
    await interaction.reply({
      content: 'An error occurred. Please try again.',
      ephemeral: true
    })
  }
}
