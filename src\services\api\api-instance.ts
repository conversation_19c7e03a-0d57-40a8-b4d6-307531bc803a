import apiService from './api-service.js'
import type { InstanceConfig } from '../../interfaces/config.js'
import { logger } from '../../utils/logging/logger.js'

// Use simple objects to store API instances and configs
const apiInstances: Record<number, ReturnType<typeof apiService>> = {}
const instanceConfigs: Record<number, InstanceConfig> = {}

export function initializeApi(
  token = '',
  xorKey = '',
  instanceConfig: InstanceConfig
): void {
  const continent = instanceConfig.continent
  logger.debug('[initializeApi] Creating instance for continent:', {
    continent,
    stackTrace: new Error().stack
  })

  apiInstances[continent] = apiService({
    token,
    xorKey,
    instanceConfig,
    onError: error => {
      instanceConfig.token = null
      instanceConfig.xorKey = null
      logger.error(`API Error for Continent ${continent}:`, error)
    }
  })

  instanceConfigs[continent] = instanceConfig
}

export function getApiInstance(continent: number) {
  if (!apiInstances[continent]) {
    throw new Error(`API instance not initialized for Continent ${continent}`)
  }
  return apiInstances[continent]
}

export function hasApiInstance(continent: number): boolean {
  return !!apiInstances[continent]
}

export function updateParams(
  token: string,
  xorKey: string,
  instanceConfig: InstanceConfig
): void {
  const continent = instanceConfig.continent
  if (apiInstances[continent]) {
    apiInstances[continent].update({ token, xorKey, instanceConfig })
  } else {
    initializeApi(token, xorKey, instanceConfig)
  }
  instanceConfigs[continent] = instanceConfig
}
