import { BotError } from 'utils/common/error.js'
import { setSettings } from 'utils/config/config-utils.js'

import { InstanceConfig } from '../../interfaces/config.js'
import { getApiInstance } from '../api/api-instance.js'

import {
 RoleStatus, RoleStatusMap 
} from './types.js'
import { TitleChangeResponse } from './types.js'

const TITLE_EXPIRATION_TIME = 10 * 60 * 1000

export async function assignTitle(
  role: string,
  ueid: string,
  instanceConfig: InstanceConfig,
  roleStatusMap: RoleStatusMap
): Promise<TitleChangeResponse> {
  const api = getApiInstance(instanceConfig.continent)

  const response = await api.post<TitleChangeResponse>('/shrine/title/change', {
    code: role === 'Architect' ? 108 : 109,
    targetKingdomId: ueid
  })

  const expirationTime = Date.now() + TITLE_EXPIRATION_TIME
  roleStatusMap[instanceConfig.continent] = {
    hasRole: true,
    roleName: role,
    ueid: ueid,
    expirationTime: expirationTime
  }

  const currentDate = new Date().toISOString().split('T')[0]
  if (instanceConfig.title.lastResetDate !== currentDate) {
    instanceConfig.title.dailyRequests = 0
    instanceConfig.title.lastResetDate = currentDate
  }

  instanceConfig.title.dailyRequests++
  instanceConfig.title.totalRequests++

  setSettings(
    instanceConfig,
    'title.dailyRequests',
    instanceConfig.title.dailyRequests
  )
  setSettings(
    instanceConfig,
    'title.totalRequests',
    instanceConfig.title.totalRequests
  )
  setSettings(
    instanceConfig,
    'title.lastResetDate',
    instanceConfig.title.lastResetDate
  )

  setTimeout(() => {
    const currentStatus = roleStatusMap[instanceConfig.continent]
    if (
      currentStatus &&
      currentStatus.ueid === ueid &&
      currentStatus.roleName === role
    ) {
      roleStatusMap[instanceConfig.continent] = {
        hasRole: false,
        roleName: '',
        ueid: '',
        expirationTime: undefined
      }
    }
  }, TITLE_EXPIRATION_TIME)

  return response
}
export function expireRole(
  role: string,
  continent: number,
  roleStatusMap: RoleStatusMap
): boolean {
  const roleStatus = roleStatusMap[continent]
  if (roleStatus && roleStatus.hasRole && roleStatus.roleName === role) {
    roleStatusMap[continent] = {
      hasRole: false,
      roleName: '',
      ueid: '',
      expirationTime: undefined
    }
    return true
  }

  if (!true) {
    throw new BotError('Error expiring role', 'EXPIRE_ROLE_ERROR')
  }

  return false
}

export function getRoleStatus(roleStatusMap: RoleStatusMap): {
  [continent: string]: RoleStatus
} {
  return { ...roleStatusMap }
}
