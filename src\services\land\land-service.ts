import axios from 'axios'
import {
 addDays, format, parse 
} from 'date-fns'

import { BotError } from '../../utils/common/error.js'

import type {
 Contribution, LandData 
} from './types.js'

interface LandResponse {
  result: boolean
  owner: string
  contribution: Contribution[]
}

export async function landInfo(
  startDate: string,
  endDate: string,
  landId: string
): Promise<LandData> {
  try {
    const start = parse(startDate, 'MM/dd/yyyy', new Date())
    const end = parse(endDate, 'MM/dd/yyyy', new Date())

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      throw new BotError(
        'Invalid date format. Please use MM/dd/yyyy',
        'INVALID_DATE_FORMAT'
      )
    }

    let currentStart = start
    const allContributions: Contribution[] = []

    while (currentStart <= end) {
      const chunkEnd = addDays(currentStart, 5)
      const effectiveEnd = chunkEnd > end ? end : chunkEnd

      const formattedStart = format(currentStart, 'MM/dd/yyyy')
      const formattedEnd = format(effectiveEnd, 'MM/dd/yyyy')

      const url = `https://api-lok-live.leagueofkingdoms.com/api/stat/land/contribution?from=${formattedStart}&to=${formattedEnd}&landId=${landId}`

      const response = await axios.get<LandResponse>(url)

      if (response.data.result && Array.isArray(response.data.contribution)) {
        allContributions.push(...response.data.contribution)
      }
      currentStart = addDays(chunkEnd, 1)
    }

    const consolidatedContributions = allContributions.reduce(
      (acc, contribution) => {
        const existing = acc.find(c => c.kingdomId === contribution.kingdomId)
        if (existing) {
          existing.total += contribution.total
        } else {
          acc.push({ ...contribution })
        }
        return acc
      },
      [] as Contribution[]
    )

    consolidatedContributions.sort((a, b) => b.total - a.total)

    return {
      [landId]: consolidatedContributions.filter(
        contribution => contribution.total > 0
      )
    }
  } catch (error) {
    throw error instanceof BotError
      ? error
      : new BotError('Failed to fetch land data', 'LAND_FETCH_ERROR')
  }
}
