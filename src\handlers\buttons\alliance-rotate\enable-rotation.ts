/* eslint-disable @typescript-eslint/no-unused-vars */
import type { ButtonInteraction } from 'discord.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types'

import type { InstanceConfig } from '../../../interfaces/config'
import {
  openAlliance,
  setupRotateMonitor
} from '../../../services/alliance/rotate-service'
import type { ApiService } from '../../../services/api/api-service'
import { setSettings } from '../../../utils/config/config-utils'
import { updateRotatePanel } from '../../../services/monitor/rotation/utils/rotate-panel-embed'
import { logger } from '../../../utils/logging/logger'

export async function handleEnableRotation(
  interaction: ButtonInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap
): Promise<void> {
  try {
    await interaction.deferUpdate()

    if (instanceConfig.rotate?.enabled) {
      await interaction.followUp({
        content: 'Alliance rotation is already active.',
        ephemeral: true
      })
      return
    }

    const result = await openAlliance(instanceConfig)
    if (result.result) {
      instanceConfig.rotate!.enabled = true
      await setSettings(instanceConfig, 'rotate.enabled', true)
      setupRotateMonitor(interaction.client, instanceConfig)

      await updateRotatePanel(interaction.client, instanceConfig)

      await interaction.followUp({
        content: 'Alliance rotation has been enabled successfully!',
        ephemeral: true
      })
    } else {
      await interaction.followUp({
        content: result.message,
        ephemeral: true
      })
    }
  } catch (error) {
    logger.error('Error enabling rotation:', error)
    await interaction.followUp({
      content: 'An error occurred while enabling rotation.',
      ephemeral: true
    })
  }
}
