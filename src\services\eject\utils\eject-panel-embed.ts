import {
    type Client, EmbedBuilder, type TextChannel 
   } from 'discord.js'
   import type { InstanceConfig } from 'interfaces/config.js'
   import { logger } from 'utils/logging/logger.js'
   
   export function createEjectPanel(instanceConfig: InstanceConfig) {
     const embed = new EmbedBuilder()
       .setTitle('Eject Manager Panel')
       .setDescription(
         `Status: ${instanceConfig.eject.enabled ? '🟢 Enabled' : '🔴 Disabled'}\n\n` +
           `**Current Settings:**\n` +
           `**How it works:**\n` +
           `• Automatically kick kingdom in CVC when active \n`
       )
       .setTimestamp()
       .setColor(instanceConfig.manage.enabled ? 0x00ff00 : 0xff0000)
   
     return { embeds: [embed] }
   }
   
   export async function updateEjectPanel(
     client: Client,
     instanceConfig: InstanceConfig
   ) {
     if (!instanceConfig.eject?.channelId || !instanceConfig.eject?.messageId) {
       return
     }
   
     try {
       const channel = (await client.channels.fetch(
         instanceConfig.eject.channelId
       )) as TextChannel
       if (!channel || !channel.isTextBased()) {
         logger.error('Invalid manager panel channel configuration.')
         return
       }
   
       const message = await channel.messages.fetch(
         instanceConfig.eject.messageId
       )
       await message.edit(createEjectPanel(instanceConfig))
     } catch (error) {
       logger.error('Error updating manager panel:', error)
     }
   }
   