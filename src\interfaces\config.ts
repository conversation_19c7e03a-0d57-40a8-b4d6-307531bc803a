import { KingdomData } from './player'

export interface VerifyConfig {
  roleName: string
  channelId: string
}

export interface ShrineConfig {
  channelId: string
  alertChannelId: string
  shrineMessageId: string | null
  shrineStatus: unknown | null
}

export interface RotateConfig {
  channelId: string
  messageId?: string
  alertChannelId: string
  isOpen?: boolean
  kickThreshold: number
  enabled: boolean | null
  maxKick: number
}

export interface StakerConfig {
  channelId: string | null
  messageId?: string | null
  lastUpdate?: number
  pledgerRole: string
  infuraProjectId: string
  etherscanApiKey: string
  contractAddress: string
}

export interface SkillConfig {
  channelId: string | null
  messageId?: string | null
  alertChannelId: string
}

export interface MailConfig {
  alertChannelId: string
}

export interface TitleConfig {
  channelId: string
  messageId: string | null
  dailyRequests: number
  totalRequests: number
  lastResetDate: string | null
}

export interface ManagerConfig {
  channelId: string
  messageId: string | null
  minPower: string | null
  enabled?: boolean
}

export interface EjectConfig {
  channelId: string
  messageId: string | null
  alertChannelId: string
  enabled?: boolean
}
export interface RankConfig {
  channelId: string
  messageId: string | null
}

export interface InstanceConfig {
  username: string
  password: string
  guild_id: string
  enabledCommands: string[]
  adminRole: string
  continent: number
  allyID: string[]
  verify: VerifyConfig
  shrine?: ShrineConfig
  title?: TitleConfig
  rotate?: RotateConfig
  staker?: StakerConfig
  skill?: SkillConfig
  mail?: MailConfig
  manage?: ManagerConfig
  eject?: EjectConfig
  rank?: RankConfig
  token: string | ''
  regionHash: string | ''
  xorKey: string | ''
  kingdomData?: KingdomData
  welcomeChannelId?: string
}

export interface BotConfig {
  bot_token: string
  client_id: string
  instances: InstanceConfig[]
}

export type Continent = number

export interface InstanceMap<T> {
  [continent: Continent]: T
}
