import { Collection } from 'discord.js'

import { logger } from '../logging/logger.js'

import type { Command } from './types.js'

// Import all command handlers directly
import verifyCommand from '../../commands/verify/handler.js'
import titlePanelCommand from '../../commands/title-panel/handler.js'
import ejectPanelCommand from '../../commands/eject-panel/handler.js'
import requestTitleCommand from '../../commands/title/handler.js'
import titleToggleCommand from '../../commands/title-toggle/handler.js'
import landCommand from '../../commands/land/handler.js'
import shrineCommand from '../../commands/shrine/handler.js'
import rotatePanelCommand from '../../commands/alliance-rotate/handler.js'
import stakerCommand from '../../commands/staker/handler.js'
import searchCommand from '../../commands/search/handler.js'
import playerDataCommand from '../../commands/player/handler.js'
import updateDataCommand from '../../commands/update/handler.js'
import summaryCommand from '../../commands/summary/handler.js'
import skillsPanelCommand from '../../commands/global-skill/handler.js'
import globalSkillCommand from '../../commands/global-skill/handler.js'
import mailCommand from '../../commands/mail/handler.js'
import allianceRankCommand from '../../commands/alliance-rank/handler.js'
import addKingdomCommand from '../../commands/add-kingdom/handler.js'
import allianceManagerCommand from '../../commands/alliance-manager/handler.js'
import setPowerCommand from '../../commands/set-power/handler.js'
import setMaxKickCommand from '../../commands/set-max-kick/handler.js'

class CommandManager {
  private commands: Collection<string, Command>

  constructor() {
    this.commands = new Collection()
  }

  async loadCommands(): Promise<void> {
    try {
      // Define all commands with their handlers
      const commandList = [
        verifyCommand,
        titlePanelCommand,
        requestTitleCommand,
        titleToggleCommand,
        landCommand,
        shrineCommand,
        rotatePanelCommand,
        stakerCommand,
        searchCommand,
        playerDataCommand,
        updateDataCommand,
        summaryCommand,
        skillsPanelCommand,
        globalSkillCommand,
        mailCommand,
        allianceRankCommand,
        addKingdomCommand,
        allianceManagerCommand,
        setPowerCommand,
        setMaxKickCommand,
        ejectPanelCommand
      ]

      // Register all commands
      for (const command of commandList) {
        if (command && command.data && command.data.name) {
          this.commands.set(command.data.name, command)
        } else {
          logger.warn(`Invalid command structure for a command`)
        }
      }

      logger.info(`Successfully loaded ${this.commands.size} commands`)
    } catch (error) {
      logger.error(`Error loading commands:`, error)
      throw error
    }
  }

  getCommands(): Collection<string, Command> {
    return this.commands
  }

  getCommand(name: string): Command | undefined {
    return this.commands.get(name)
  }
}

export const commandManager = new CommandManager()
