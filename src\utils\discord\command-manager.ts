import { Collection } from 'discord.js'
import { readdir } from 'fs/promises'
import { join } from 'path'
import { pathToFileURL } from 'url'

import { logger } from '../logging/logger.js'

import type { Command } from './types.js'

class CommandManager {
  private commands: Collection<string, Command>

  constructor() {
    this.commands = new Collection()
  }

  async loadCommands(): Promise<void> {
    try {
      const commandsPath = join(process.cwd(), 'src', 'commands')
      const commandDirs = await this.getCommandDirectories(commandsPath)

      logger.debug(`Found ${commandDirs.length} command directories`)

      for (const commandDir of commandDirs) {
        try {
          const command = await this.loadCommand(commandDir)
          if (command) {
            this.commands.set(command.data.name, command)
            logger.debug(`Loaded command: ${command.data.name}`)
          }
        } catch (error) {
          logger.warn(`Failed to load command from ${commandDir}:`, error)
        }
      }

      logger.info(`Successfully loaded ${this.commands.size} commands`)
    } catch (error) {
      logger.error(`Error loading commands:`, error)
      throw error
    }
  }

  private async getCommandDirectories(commandsPath: string): Promise<string[]> {
    try {
      const entries = await readdir(commandsPath, { withFileTypes: true })
      return entries
        .filter(entry => entry.isDirectory())
        .map(entry => join(commandsPath, entry.name))
    } catch (error) {
      logger.error(`Error reading commands directory:`, error)
      return []
    }
  }

  private async loadCommand(commandDir: string): Promise<Command | null> {
    try {
      const handlerPath = join(commandDir, 'handler.js')
      const handlerUrl = pathToFileURL(handlerPath).href

      const commandModule = await import(handlerUrl)
      const command = commandModule.default

      if (!command || !command.data || !command.data.name) {
        logger.warn(`Invalid command structure in ${handlerPath}`)
        return null
      }

      return command
    } catch (error) {
      logger.debug(`Could not load command from ${commandDir}:`, error)
      return null
    }
  }

  getCommands(): Collection<string, Command> {
    return this.commands
  }

  getCommand(name: string): Command | undefined {
    return this.commands.get(name)
  }
}

export const commandManager = new CommandManager()
