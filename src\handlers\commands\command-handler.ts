import type {
  ChatInputCommandInteraction,
  InteractionReplyOptions,
  TextChannel,
  Message,
  EmbedBuilder,
  ActionRowBuilder,
  ButtonBuilder
} from 'discord.js'
import { SocketService } from 'services/socket/service.js'
import type {
 RoleStatusMap, TitleToggleMap
} from 'services/title/types.js'

import type { InstanceConfig } from '../../interfaces/config.js'
import type { CommandContext } from '../../interfaces/context.js'
import { updateParams } from '../../services/api/api-instance.js'
import type { ApiService } from '../../services/api/api-service.js'
import { login } from '../../services/auth/login-service.js'
import { BotError } from '../../utils/common/error.js'
import { decodeRegionHash } from '../../utils/decode.js'
import {
  CommandUtils,
  verifyCommandChannel
} from '../../utils/discord/command-utils.js'
import { InteractionErrorHandler } from '../../utils/discord/interaction-error-handler.js'
import {
  MessageUtils,
  type MessageContent
} from '../../utils/discord/message-utils.js'
import { logger } from '../../utils/logging/logger.js'

export abstract class BaseCommandHandler {
  readonly logger = logger
  protected readonly context: CommandContext

  constructor(context: CommandContext)
  constructor(
    interaction: ChatInputCommandInteraction,
    instanceConfig: InstanceConfig,
    apiInstance: ApiService,
    titleToggleMap?: TitleToggleMap,
    roleStatusMap?: RoleStatusMap,
    socketService?: SocketService
  )
  constructor(
    contextOrInteraction: CommandContext | ChatInputCommandInteraction,
    instanceConfig?: InstanceConfig,
    apiInstance?: ApiService,
    titleToggleMap?: TitleToggleMap,
    roleStatusMap?: RoleStatusMap,
    socketService?: SocketService
  ) {
    if ('interaction' in contextOrInteraction) {
      // New context-based constructor
      this.context = contextOrInteraction
    } else {
      // Legacy parameter-based constructor for backward compatibility
      this.context = {
        interaction: contextOrInteraction,
        instanceConfig: instanceConfig!,
        apiInstance: apiInstance!,
        titleToggleMap,
        roleStatusMap,
        socketService
      }
    }
  }

  // Convenient getters for backward compatibility
  get interaction(): ChatInputCommandInteraction {
    return this.context.interaction
  }

  get instanceConfig(): InstanceConfig {
    return this.context.instanceConfig
  }

  get apiInstance(): ApiService {
    return this.context.apiInstance
  }

  get titleToggleMap(): TitleToggleMap | undefined {
    return this.context.titleToggleMap
  }

  get roleStatusMap(): RoleStatusMap | undefined {
    return this.context.roleStatusMap
  }

  get socketService(): SocketService | undefined {
    return this.context.socketService
  }

  abstract execute(): Promise<void>

  async ensureToken(): Promise<void> {
    if (!this.instanceConfig.token) {
      await this.refreshToken()
    }
  }

  async refreshToken(): Promise<void> {
    const result = await login(this.instanceConfig)
    if (!result) {
      throw new BotError('Login failed', 'LOGIN_ERROR')
    }
    this.instanceConfig.token = result.token
    this.instanceConfig.xorKey = decodeRegionHash(result.regionHash)
    updateParams(
      this.instanceConfig.token,
      this.instanceConfig.xorKey,
      this.instanceConfig
    )
    this.logger.debug('instanceConfig in base-command-handler', {
      instanceConfig: this.instanceConfig
    })
  }

  async replyWithFlags(options: InteractionReplyOptions): Promise<void> {
    const updatedOptions: InteractionReplyOptions = {
      ...options,
      flags: options.ephemeral ? 64 : undefined
    }
    delete updatedOptions.ephemeral
    await this.interaction.reply(updatedOptions)
  }

  async validateRole(roleName: string): Promise<boolean> {
    return CommandUtils.validateRole(this.interaction, roleName)
  }

  async verifyCommandChannel(commandName: string): Promise<boolean> {
    return verifyCommandChannel(
      this.interaction,
      this.instanceConfig,
      commandName
    )
  }

  async handleError(error: unknown, customMessage?: string): Promise<void> {
    await InteractionErrorHandler.handleCommandError(
      this.interaction,
      error,
      this.interaction.commandName,
      { customMessage }
    )
  }

  async sendDiscordNotification(
    channelId: string,
    message: string
  ): Promise<void> {
    if (!channelId) {
      this.logger.warn('Notification channel not configured.')
      return
    }

    const channel = await this.interaction.client.channels.fetch(channelId)
    if (!channel || !channel.isTextBased()) {
      this.logger.error('Invalid notification channel configuration.')
      return
    }

    await (channel as TextChannel).send(message)
  }

  async deferReply(ephemeral = false): Promise<void> {
    if (!this.interaction.replied && !this.interaction.deferred) {
      await this.interaction.deferReply({ ephemeral })
    }
  }

  messageUtils = {
    updateOrSend: (
      channel: TextChannel,
      content: MessageContent,
      messageId?: string | null
    ): Promise<Message | null> =>
      MessageUtils.updateOrSendMessage(channel, content, messageId),

    delete: (channel: TextChannel, messageId: string): Promise<boolean> =>
      MessageUtils.deleteMessage(channel, messageId),

    createEmbed: (
      title: string,
      description: string,
      fields?: { name: string; value: string; inline?: boolean }[]
    ): EmbedBuilder => MessageUtils.createEmbed(title, description, fields),

    createActionRow: (
      ...buttons: ButtonBuilder[]
    ): ActionRowBuilder<ButtonBuilder> =>
      MessageUtils.createActionRow(...buttons),

    createButton: (
      customId: string,
      label: string,
      style: number
    ): ButtonBuilder => MessageUtils.createButton(customId, label, style)
  }
}
