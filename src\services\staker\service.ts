import fs from 'fs/promises'
import path from 'path'

import type { Client } from 'discord.js'
import * as ethers from 'ethers'
const { JsonRpcProvider, Contract, formatUnits } = ethers

import type { InstanceConfig } from '../../interfaces/config'
import { updateStakerPanelEmbed } from './utils/staker-panel-embed'
import { logger } from '../../utils/logging/logger'

import type { Staker } from './types'

const BATCH_SIZE = 5 // Number of concurrent requests
const DELAY_BETWEEN_BATCHES = 1000 // 1 second delay between batches
const MAX_RETRIES = 3 // Maximum number of retries per request

export class StakerService {
  verifiedStakersPath: string
  provider: ethers.JsonRpcProvider
  contract: ethers.Contract

  constructor(public instanceConfig: InstanceConfig) {
    const INFURA_PROJECT_ID = '********************************'
    const CONTRACT_ADDRESS = '******************************************'

    const infuraUrl = `https://mainnet.infura.io/v3/${INFURA_PROJECT_ID}`
    // Remove FetchRequest and directly use the URL
    this.provider = new JsonRpcProvider(infuraUrl, 'mainnet', {
      staticNetwork: true
    })
    this.contract = new Contract(CONTRACT_ADDRESS, this.getABI(), this.provider)

    const dataDir = path.join(process.cwd(), 'data')
    this.verifiedStakersPath = path.join(
      dataDir,
      `${instanceConfig.continent}_verified_stakers.json`
    )
  }

  getABI() {
    return [
      'function getStakers() view returns (address[])',
      'function stakeOf(address owner, uint256 continent) view returns (uint256)'
    ]
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private async fetchStakeWithRetry(
    address: string,
    retryCount = 0
  ): Promise<string> {
    try {
      const stake = await this.contract.stakeOf.staticCall(
        address,
        this.instanceConfig.continent
      )
      return stake.toString()
    } catch (error) {
      if (retryCount < MAX_RETRIES) {
        logger.warn(`Retry ${retryCount + 1} for address ${address}`)
        await this.sleep(1000 * (retryCount + 1)) // Exponential backoff
        return this.fetchStakeWithRetry(address, retryCount + 1)
      }
      throw error
    }
  }

  private async processBatch(addresses: string[]): Promise<Staker[]> {
    const stakers: Staker[] = []

    for (const address of addresses) {
      try {
        const stake = await this.fetchStakeWithRetry(address)
        stakers.push({
          address,
          stake,
          rank: ''
        })
        logger.debug(`Processed address ${address}`)
      } catch (error) {
        logger.error(`Failed to fetch stake for address ${address}:`, error)
      }
    }

    return stakers
  }

  async updateStakerInformation(client: Client): Promise<void> {
    try {
      await this.provider.ready
      logger.info('Provider is ready')

      const currentStakers = await this.fetchCurrentStakers()
      logger.info(`Fetched ${currentStakers.length} current stakers`)

      const verifiedStakers = await this.fetchVerifiedStakers()
      logger.info(`Fetched ${verifiedStakers.length} verified stakers`)

      const mergedStakers = this.mergeStakerData(
        currentStakers,
        verifiedStakers
      )
      logger.info(`Merged ${mergedStakers.length} stakers`)

      const totalLOKA = this.calculateTotalLOKA(mergedStakers)
      const totalLOKAFormatted = formatUnits(totalLOKA, 18)
      logger.info(`Calculated total LOKA: ${totalLOKAFormatted}`)

      await updateStakerPanelEmbed(
        client,
        this.instanceConfig,
        mergedStakers,
        totalLOKAFormatted
      )
      logger.info('Updated staker panel embed')

      await this.updateVerifiedStakers(mergedStakers)
      logger.info('Updated verified stakers')
    } catch (error) {
      logger.error('Error in updateStakerInformation:', error)
      throw new Error(
        `Failed to update staker information: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  mergeStakerData(
    currentStakers: Staker[],
    verifiedStakers: Staker[]
  ): Staker[] {
    const verifiedMap = new Map(
      verifiedStakers.map(vs => [vs.address.toLowerCase(), vs])
    )

    return currentStakers
      .sort((a, b) => (BigInt(b.stake) > BigInt(a.stake) ? 1 : -1))
      .map((staker, index: number) => {
        const verifiedStaker = verifiedMap.get(staker.address.toLowerCase())
        return {
          ...staker,
          rank: `Rank ${index + 1}`,
          comment: verifiedStaker?.comment
        }
      })
  }

  calculateTotalLOKA(stakers: Staker[]): bigint {
    return stakers.reduce(
      (total, staker) => total + BigInt(staker.stake),
      BigInt(0)
    )
  }

  async fetchCurrentStakers(): Promise<Staker[]> {
    const addresses = await this.contract.getStakers()
    logger.info(`Fetching stakes for ${addresses.length} addresses`)

    const allStakers: Staker[] = []

    for (let i = 0; i < addresses.length; i += BATCH_SIZE) {
      const batchAddresses = addresses.slice(i, i + BATCH_SIZE)
      logger.info(
        `Processing batch ${i / BATCH_SIZE + 1} of ${Math.ceil(addresses.length / BATCH_SIZE)}`
      )

      const batchStakers = await this.processBatch(batchAddresses)
      allStakers.push(
        ...batchStakers.filter(staker => BigInt(staker.stake) > 0)
      )

      if (i + BATCH_SIZE < addresses.length) {
        await this.sleep(DELAY_BETWEEN_BATCHES)
      }
    }

    logger.info(
      `Successfully fetched ${allStakers.length} staker records with stake > 0`
    )
    return allStakers
  }

  async fetchVerifiedStakers(): Promise<Staker[]> {
    try {
      const data = await fs.readFile(this.verifiedStakersPath, 'utf-8')
      return JSON.parse(data)
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        return []
      }
      throw error
    }
  }

  async updateVerifiedStakers(verifiedStakers: Staker[]): Promise<void> {
    try {
      await fs.mkdir(path.dirname(this.verifiedStakersPath), {
        recursive: true
      })
      await fs.writeFile(
        this.verifiedStakersPath,
        JSON.stringify(verifiedStakers, null, 2)
      )
      logger.info(`Updated verified stakers file: ${this.verifiedStakersPath}`)
    } catch (error) {
      logger.error(`Failed to update verified stakers file: ${error}`)
      throw new Error(`Failed to update verified stakers: ${error}`)
    }
  }
}
