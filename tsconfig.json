{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "strictNullChecks": false, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "outDir": "./dist", "rootDir": "./src", "resolveJsonModule": true, "baseUrl": "./src", "paths": {"*": ["*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"]}