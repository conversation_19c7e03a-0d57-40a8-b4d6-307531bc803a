import { BaseCommandHandler } from '../../handlers/commands/command-handler'
import { setSettings } from '../../utils/config/config-utils'
import { updateManagerPanel } from '../../services/monitor/manager/utils/manager-panel-embed'
import { logger } from '../../utils/logging/logger'

export class HandleSetPower extends BaseCommandHandler {
  async execute(): Promise<void> {
    if (!(await this.validateRole(this.instanceConfig.adminRole))) return

    const newPower = this.interaction.options.getString('power', true)

    if (isNaN(Number(newPower))) {
      await this.interaction.reply({
        content: 'Please provide a valid number for the minimum power.',
        ephemeral: true
      })
      return
    }

    try {
      this.instanceConfig.manage.minPower = newPower
      await setSettings(this.instanceConfig, 'manage.minPower', newPower)

      await updateManagerPanel(this.interaction.client, this.instanceConfig)

      await this.interaction.reply({
        content: `Minimum power has been set to ${newPower}.`,
        ephemeral: true
      })
      logger.info(
        `Minimum power set to ${newPower} for continent ${this.instanceConfig.continent}`
      )
    } catch (error) {
      logger.error('Error setting minimum power:', error)
      await this.interaction.reply({
        content: 'An error occurred while setting the minimum power.',
        ephemeral: true
      })
    }
  }
}

export default {
  data: {
    name: 'set_power',
    description: 'Set the minimum power for alliance applications',
    options: [
      {
        name: 'power',
        type: 3, // STRING type
        description: 'The minimum power required for alliance applications',
        required: true
      }
    ]
  },
  CommandHandlerClass: HandleSetPower
}
