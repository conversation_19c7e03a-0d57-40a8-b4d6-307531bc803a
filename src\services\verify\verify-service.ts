import type { InstanceConfig } from '../../interfaces/config.js'
import type { PlayerProfile } from '../../interfaces/player.js'
import { BotError } from '../../utils/common/error.js'
import { logger } from '../../utils/logging/logger.js'
import { fetchPlayerProfile } from '../alliance/alliance-service.js'
import {
  addOrUpdatePlayerProfile,
  getPlayerProfiles
} from '../storage/user-data-storage.js'

export async function verifyUser(
  instanceConfig: InstanceConfig,
  kingdomId: string,
  memberId: string
): Promise<PlayerProfile & { isVerified: boolean; message: string }> {
  const playerProfile = await fetchPlayerProfile(instanceConfig, kingdomId)

  if (!playerProfile) {
    throw new BotError('Failed to fetch player data', 'VERIFY_DATA_ERROR')
  }

  const profileContinent = String(playerProfile.continent).trim()
  const configContinent = String(instanceConfig.continent).trim()

  let isVerified = false
  let message = ''

  if (profileContinent === configContinent) {
    const existingProfiles = await getPlayerProfiles(instanceConfig, memberId)
    const existingProfile = existingProfiles.find(
      profile => profile.id === kingdomId
    )

    if (existingProfile) {
      isVerified = true
      message = `${playerProfile.name} is already verified for Continent ${configContinent}.`
    } else {
      await addOrUpdatePlayerProfile(instanceConfig, memberId, playerProfile)
      isVerified = true
      message = `${playerProfile.name} has been verified for Continent ${configContinent}.`
    }

    logger.info(`User verification status`, {
      kingdomId,
      memberId,
      continent: configContinent,
      isVerified
    })
  } else {
    message = `${playerProfile.name} belongs to Continent ${profileContinent}, not the Continent ${configContinent}.`
    logger.warn(`Continent mismatch for verification`, {
      kingdomId,
      expectedContinent: configContinent,
      actualContinent: profileContinent
    })
  }

  return { ...playerProfile, isVerified, message }
}
