import { InstanceConfig } from '../../interfaces/config.js'
import { logger } from '../logging/logger.js'

interface MonitorOptions {
  interval: number
  onTick: () => Promise<void>
  onError?: (error: Error) => void
  initialDelay?: number
  maxRetries?: number
  retryDelay?: number
}

export class Monitor {
  private config: InstanceConfig
  private interval: number
  private onTick: () => Promise<void>
  private onError?: (error: Error) => void
  private timer?: NodeJS.Timeout
  private isRunning: boolean
  private lastRun: number
  private initialDelay: number
  private retryCount: number
  private maxRetries: number
  private retryDelay: number

  constructor(config: InstanceConfig, options: MonitorOptions) {
    this.config = config
    this.interval = options.interval
    this.onTick = options.onTick
    this.onError = options.onError
    this.isRunning = false
    this.lastRun = 0
    this.initialDelay = options.initialDelay || 0
    this.retryCount = 0
    this.maxRetries = options.maxRetries || 3
    this.retryDelay = options.retryDelay || 5000
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn(
        `Monitor already running for Continent ${this.config.continent}`
      )
      return
    }

    this.isRunning = true
    logger.info(`Monitor starting for Continent ${this.config.continent}`)

    if (this.initialDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, this.initialDelay))
    }

    await this.runTick()
    this.scheduleNextTick()
  }

  stop(): void {
    if (!this.isRunning) {
      return
    }

    if (this.timer) {
      clearTimeout(this.timer)
    }

    this.isRunning = false
    this.retryCount = 0
    logger.info(`Monitor stopped for Continent ${this.config.continent}`)
  }

  isActive(): boolean {
    return this.isRunning
  }

  getLastRunTime(): number {
    return this.lastRun
  }

  getRetryCount(): number {
    return this.retryCount
  }

  private async runTick(): Promise<void> {
    try {
      await this.onTick()
      this.lastRun = Date.now()
      this.retryCount = 0 // Reset retry count on successful execution
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error))
      logger.error(`Monitor error for Continent ${this.config.continent}:`, err)

      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        logger.info(
          `Retrying monitor tick for Continent ${this.config.continent} (attempt ${this.retryCount})`
        )
        await new Promise(resolve => setTimeout(resolve, this.retryDelay))
        await this.runTick()
        return
      }

      if (this.onError) {
        this.onError(err)
      }
    }
  }

  private scheduleNextTick(): void {
    if (!this.isRunning) {
      return
    }

    this.timer = setTimeout(async () => {
      await this.runTick()
      this.scheduleNextTick()
    }, this.interval)
  }
}
