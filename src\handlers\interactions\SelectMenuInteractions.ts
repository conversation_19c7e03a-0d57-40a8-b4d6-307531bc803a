import type { StringSelectMenuInteraction } from 'discord.js'
import { SocketService } from 'services/socket/service.js'
import type {
 RoleStatusMap, TitleToggleMap 
} from 'services/title/types.js'

import type { InstanceConfig } from '../../interfaces/config.js'
import type { ApiService } from '../../services/api/api-service.js'
import { logger } from '../../utils/logging/logger.js'
import { handleAllianceRankMemberSelection } from '../components/select-alliance-member.js'
import { handleAllianceRankChange } from '../components/select-alliance-rank.js'
import { handleKingdomSelection } from '../components/select-kingdom-menu.js'
import { handleRankVerification } from '../components/select-staker-rank.js'

type SelectMenuHandlerFunction = (
  interaction: StringSelectMenuInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap,
  socketService?: SocketService | null
) => Promise<void>

const selectMenuHandlers: Record<string, SelectMenuHandlerFunction> = {
  'select-member': handleAllianceRankMemberSelection,
  'select-rank-': handleAllianceRankChange,
  'select-kingdom': handleKingdomSelection,
  'staker-comments': handleRankVerification
}

export async function handleSelectMenu(
  interaction: StringSelectMenuInteraction,
  instanceConfig: InstanceConfig,
  apiInstance: ApiService,
  titleToggleMap: TitleToggleMap,
  roleStatusMap: RoleStatusMap,
  socketService?: SocketService
): Promise<void> {
  const handlerId = Object.keys(selectMenuHandlers).find(
    id => interaction.customId === id || interaction.customId.startsWith(id)
  )

  if (handlerId) {
    try {
      await selectMenuHandlers[handlerId](
        interaction,
        instanceConfig,
        apiInstance,
        titleToggleMap,
        roleStatusMap,
        socketService
      )
    } catch (error) {
      logger.error(`Error handling select menu interaction:`, error)
      // Only send error reply if interaction hasn't been replied to
      if ((error as { code?: string })?.code !== 'InteractionAlreadyReplied') {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: 'An error occurred while processing the select menu interaction',
            ephemeral: true
          })
        } else if (interaction.deferred) {
          await interaction.editReply({
            content: 'An error occurred while processing the select menu interaction'
          })
        }
      }
    }
  } else {
    logger.warn(`Unknown select menu action: ${interaction.customId}`)
    await interaction.reply({
      content: 'Unknown select menu action',
      ephemeral: true
    })
  }
}
