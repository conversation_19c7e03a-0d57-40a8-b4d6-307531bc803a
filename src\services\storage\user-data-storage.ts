import fs from 'fs/promises'
import path from 'path'

import type { InstanceConfig } from '../../interfaces/config'
import type {
 UserData, PlayerProfile 
} from '../../interfaces/player'
import { logger } from '../../utils/logging/logger'

const DATA_DIR = path.join(process.cwd(), 'data')

async function ensureDataFile(instanceConfig: InstanceConfig): Promise<void> {
  try {
    await fs.mkdir(DATA_DIR, { recursive: true })
    const filePath = getUserDataFilePath(instanceConfig)
    await fs.access(filePath).catch(() => fs.writeFile(filePath, '{}', 'utf8'))
  } catch (error) {
    logger.error('Error ensuring data file:', error)
    throw error
  }
}

function getUserDataFilePath(instanceConfig: InstanceConfig): string {
  return path.join(DATA_DIR, `${instanceConfig.continent}_user_data.json`)
}

async function readUserData(instanceConfig: InstanceConfig): Promise<UserData> {
  await ensureDataFile(instanceConfig)
  const filePath = getUserDataFilePath(instanceConfig)
  const data = await fs.readFile(filePath, 'utf8')
  return JSON.parse(data)
}

async function writeUserData(
  instanceConfig: InstanceConfig,
  userData: UserData
): Promise<void> {
  const filePath = getUserDataFilePath(instanceConfig)
  await fs.writeFile(filePath, JSON.stringify(userData, null, 2), 'utf8')
}

export async function addOrUpdatePlayerProfile(
  instanceConfig: InstanceConfig,
  discordId: string,
  profile: PlayerProfile
): Promise<void> {
  const userData = await readUserData(instanceConfig)
  if (!userData[discordId]) {
    userData[discordId] = []
  }
  const existingProfileIndex = userData[discordId].findIndex(
    p => p.id === profile.id
  )
  if (existingProfileIndex !== -1) {
    userData[discordId][existingProfileIndex] = {
      ...userData[discordId][existingProfileIndex],
      ...profile,
      power: Number(profile.power) || undefined,
      kill: Number(profile.kill) || undefined,
      death: Number(profile.death) || undefined,
      gathering: Number(profile.gathering) || undefined,
      individualRank:
        profile.individualRank !== null ? Number(profile.individualRank) : null,
      continentRank:
        profile.continentRank !== null ? Number(profile.continentRank) : null,
      individualPoints:
        profile.individualPoints !== null
          ? Number(profile.individualPoints)
          : null
    }
  } else {
    userData[discordId].push({
      ...profile,
      power: Number(profile.power) || undefined,
      kill: Number(profile.kill) || undefined,
      death: Number(profile.death) || undefined,
      gathering: Number(profile.gathering) || undefined,
      individualRank:
        profile.individualRank !== null ? Number(profile.individualRank) : null,
      continentRank:
        profile.continentRank !== null ? Number(profile.continentRank) : null,
      individualPoints:
        profile.individualPoints !== null
          ? Number(profile.individualPoints)
          : null
    })
  }
  await writeUserData(instanceConfig, userData)
}

export async function getPlayerProfiles(
  instanceConfig: InstanceConfig,
  discordId: string
): Promise<PlayerProfile[]> {
  const userData = await readUserData(instanceConfig)
  return userData[discordId] || []
}

export async function getAllPlayerProfiles(
  instanceConfig: InstanceConfig
): Promise<UserData> {
  return await readUserData(instanceConfig)
}

export async function removePlayerProfile(
  instanceConfig: InstanceConfig,
  discordId: string,
  kingdomId: string
): Promise<void> {
  const userData = await readUserData(instanceConfig)
  if (userData[discordId]) {
    userData[discordId] = userData[discordId].filter(
      profile => profile.id !== kingdomId
    )
    await writeUserData(instanceConfig, userData)
  }
}

export async function updatePlayerDataWithRanks(
  instanceConfig: InstanceConfig,
  rankData: PlayerProfile[]
): Promise<void> {
  const userData = await readUserData(instanceConfig)

  for (const [discordId, profiles] of Object.entries(userData)) {
    userData[discordId] = profiles.map(profile => {
      const rankInfo = rankData.find(r => r.id === profile.id)
      if (rankInfo) {
        return {
          ...profile,
          individualRank: rankInfo.individualRank,
          continentRank: rankInfo.continentRank,
          individualPoints: rankInfo.individualPoints
        }
      }
      return profile
    })
  }

  await writeUserData(instanceConfig, userData)
  logger.info(
    `Updated user data with ranks. Total profiles updated: ${rankData.length}`
  )
}

export async function cleanUserDataByContinent(
  instanceConfig: InstanceConfig
): Promise<void> {
  const userData = await readUserData(instanceConfig)

  for (const [discordId, profiles] of Object.entries(userData)) {
    userData[discordId] = profiles.filter(
      profile => profile.continent === instanceConfig.continent
    )
  }

  await writeUserData(instanceConfig, userData)
  logger.info(
    `Cleaned user data. Removed entries not matching Continent ${instanceConfig.continent}`
  )
}
