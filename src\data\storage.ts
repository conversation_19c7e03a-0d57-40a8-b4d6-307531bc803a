import {
 writeFile, readFile 
} from 'fs/promises'
import { join } from 'path'

import { logger } from '../utils/logging/logger.js'

export class Storage<T> {
  private filename: string
  private data: T | null = null

  constructor(filename: string) {
    this.filename = join(__dirname, '..', '..', 'data', filename)
  }

  async load(): Promise<T | null> {
    try {
      const content = await readFile(this.filename, 'utf-8')
      this.data = JSON.parse(content)
      return this.data
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {
        logger.error(`Error loading data from ${this.filename}:`, error)
      }
      return null
    }
  }

  async save(data: T): Promise<boolean> {
    try {
      await writeFile(this.filename, JSON.stringify(data, null, 2))
      this.data = data
      return true
    } catch (error) {
      logger.error(`Error saving data to ${this.filename}:`, error)
      return false
    }
  }

  getData(): T | null {
    return this.data
  }
}
