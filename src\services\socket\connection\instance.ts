import type { InstanceConfig } from "../../../interfaces/config.js"
import { logger } from "../../../utils/logging/logger.js"
import { registerMessageHandlers } from "../handlers/index.js"
import { SocketService } from "../service.js"

const socketInstances: Record<number, SocketService> = {}

export function initializeSocketService(instanceConfig: InstanceConfig): SocketService {
  const continent = instanceConfig.continent
  logger.info(`[WsInstance] Initializing socket service for Continent ${continent}`)

  const socketService = new SocketService(instanceConfig)
  registerMessageHandlers(socketService)
  socketInstances[continent] = socketService

  return socketService
}

export function getSocketService(continent: number): SocketService {
  if (!socketInstances[continent]) {
    throw new Error(`Socket service not initialized for Continent ${continent}`)
  }
  return socketInstances[continent]
}

export function hasSocketService(continent: number): boolean {
  return !!socketInstances[continent]
}

export function closeSocketService(continent: number): void {
  if (socketInstances[continent]) {
    socketInstances[continent].close()
    delete socketInstances[continent]
    logger.info(`[WsInstance] Closed socket service for Continent ${continent}`)
  }
}

export function closeAllSocketServices(): void {
  Object.keys(socketInstances).forEach((key) => {
    const continent = Number(key)
    closeSocketService(continent)
  })
  logger.info("[WsInstance] Closed all socket services")
}

